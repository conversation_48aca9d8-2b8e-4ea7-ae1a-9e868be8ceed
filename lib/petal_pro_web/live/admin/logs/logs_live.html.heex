<.admin_layout current_page={:admin_logs} current_user={@current_user}>
  <.page_header title={gettext("Logs")} />

  <div class="mb-8">
    <.form for={@search_form} phx-change="search" phx-submit="search">
      <div class="flex gap-x-5">
        <div class="w-1/2 lg:w-1/4">
          <.field
            type="select"
            field={@search_form[:action]}
            options={PetalPro.Logs.Log.action_options() |> Enum.sort()}
            prompt={gettext("Select an activity type...")}
            label={gettext("Filter")}
          />
        </div>

        <div class="flex w-1/2 gap-x-3 lg:w-3/4">
          <div class="w-full lg:w-1/3">
            <.field
              type="number"
              field={@search_form[:user_id]}
              autocomplete="off"
              label={gettext("User ID")}
            />
          </div>
        </div>
      </div>

      <.field
        type="checkbox"
        field={@search_form[:enable_live_logs]}
        label={gettext("Live logs")}
      />
    </.form>

    <%= if length(Map.keys(@search_form.source.changes)) > 0 do %>
      <.link
        patch={~p"/admin/logs?#{build_filter_params(@meta)}"}
        class="text-xs text-red-500 hover:underline"
      >
        Reset filters
      </.link>
    <% end %>
  </div>

  <.data_table
    meta={@meta}
    items={@logs}
    page_size_options={[]}
    base_url_params={@search_form.source.changes}
  >
    <:col :let={log} label={gettext("Time")} field={:inserted_at} sortable>
      {Timex.from_now(log.inserted_at)}
    </:col>
    <:col :let={log} label={gettext("User")} field={:user_name} sortable>
      <%= cond do %>
        <% log.user -> %>
          <div class="flex items-center gap-2">
            <.avatar size="sm" src={user_avatar_url(log.user)} name={user_name(log.user)} />

            <.link
              class="font-medium hover:underline"
              patch={
                ~p"/admin/logs?#{build_filter_params(@meta, @search_form.source.changes, %{:user_id => log.user_id})}"
              }
              phx-hook="TippyHook"
              id={"filter-user-#{log.id}"}
              data-tippy-content={"Filter logs by this user's ID (#{log.user_id})"}
            >
              {user_name(log.user) || "New member"}
            </.link>
          </div>
        <% log.user_id -> %>
          User fully deleted
        <% true -> %>
          <div class="flex items-center gap-2">
            <.avatar size="sm" name="🤖" /> System
          </div>
      <% end %>
    </:col>
    <:col :let={log} label="" field={:user_name} class="w-4">
      <div id={"user-#{log.id}"}>
        <%= cond do %>
          <% log.user -> %>
            <.icon_button
              size="xs"
              link_type="a"
              to={~p"/admin/users/#{log.user}"}
              target="_blank"
              phx-hook="TippyHook"
              id={"edit-user-#{log.id}"}
              data-tippy-content={"Edit #{user_name(log.user)} in a new tab"}
            >
              <.icon name="hero-user-solid" class="m-0.5 h-4 w-4" />
            </.icon_button>
          <% true -> %>
        <% end %>
      </div>
    </:col>
    <:col :let={log} label={gettext("Org")} field={:org_name} sortable>
      <%= cond do %>
        <% log.org -> %>
          <.link
            class="grow font-medium hover:underline"
            patch={
              ~p"/admin/logs?#{build_filter_params(@meta, @search_form.source.changes, %{:org_id => log.org_id})}"
            }
            phx-hook="TippyHook"
            id={"filter-org-#{log.id}"}
            data-tippy-content={"Filter logs by this org's ID (#{log.org_id})"}
          >
            {org_name(log.org) || "N/A"}
          </.link>
        <% log.org_id -> %>
          Org fully deleted
        <% true -> %>
      <% end %>
    </:col>
    <:col :let={log} label="" field={:org_name} class="w-4">
      <%= cond do %>
        <% log.org -> %>
          <.icon_button
            size="xs"
            link_type="a"
            to={~p"/admin/orgs/#{log.org}"}
            target="_blank"
            phx-hook="TippyHook"
            id={"edit-org-#{log.id}"}
            data-tippy-content={"Edit #{org_name(log.org)} in a new tab"}
          >
            <.icon name="hero-building-office-2-solid" class="m-0.5 h-4 w-4" />
          </.icon_button>
        <% true -> %>
      <% end %>
    </:col>
    <:col :let={log} label={gettext("Action")} field={:action} sortable>
      <.link
        patch={
          ~p"/admin/logs?#{build_filter_params(@meta, @search_form.source.changes, %{:action => log.action})}"
        }
        class="font-medium hover:underline"
        phx-hook="TippyHook"
        id={"filter-action-#{log.action}-#{log.id}"}
        data-tippy-content={"Filter logs to #{log.action}"}
      >
        {log.action}
      </.link>

      {maybe_add_emoji(log.action)}

      <%= if log.user_type == :admin do %>
        <span>(as a mod)</span>
      <% end %>
    </:col>
    <:col :let={log} label={gettext("Extra details")}>
      <div
        :if={
          Ecto.assoc_loaded?(log.target_user) && log.target_user && log.target_user != log.user
        }
        class="flex items-center gap-2"
      >
        <.link
          patch={
            ~p"/admin/logs?#{build_filter_params(@meta, @search_form.source.changes, %{:user_id => log.target_user.id})}"
          }
          class="font-medium hover:underline"
        >
          {user_name(log.target_user) |> Util.truncate(length: 30)}
        </.link>

        <.icon_button
          size="xs"
          link_type="a"
          to={~p"/admin/users/#{log.target_user}"}
          target="_blank"
          phx-hook="TippyHook"
          id={"edit-user-#{log.id}"}
          data-tippy-content={"Edit #{user_name(log.target_user)} in a new tab"}
        >
          <.icon name="hero-user-solid" class="m-0.5 h-4 w-4" />
        </.icon_button>
      </div>

      <span>{highlight_metadata(log.action, log.metadata)}</span>
    </:col>
  </.data_table>

  <%= if @load_more do %>
    <div class="mx-auto mt-5 w-full">
      <.button color="gray" variant="outline" phx-click="load-more" class="w-full">
        <span class="phx-click-loading:hidden">
          Show More
        </span>
        <span class="hidden phx-click-loading:block">
          <.spinner />
        </span>
      </.button>
    </div>
  <% end %>
</.admin_layout>
