<%!-- This should match max_width in `public.html.heex`. Options: ["sm", "md", "lg", "xl", "full"] --%>
<% max_width = "xl" %>
<LandingPageComponents.hero
  image_src_light={~p"/images/landing_page/dashboard_light.png"}
  image_src_dark={~p"/images/landing_page/dashboard_dark.png"}
  logo_cloud_title={gettext("Trusted by brands all over the world")}
  max_width={max_width}
>
  <:title>
    <span class="">{gettext("The secret of")}</span>
    <span class="from-primary-800 via-primary-600 to-primary-400 bg-gradient-to-r bg-clip-text text-transparent dark:from-primary-600 dark:via-primary-400 dark:to-primary-200">
      {gettext("getting ahead")}
    </span>
    <span>{gettext("is getting started")}.</span>
  </:title>
  <:action_buttons>
    <.button
      label={gettext("Get started")}
      link_type="a"
      color="primary"
      to={~p"/auth/register"}
      size="lg"
    />

    <.button
      class="!border-transparent semi-translucent group bg-transparent dark:bg-transparent dark:hover:bg-gray-500/20"
      link_type="a"
      color="light"
      to="https://docs.petal.build"
      size="lg"
    >
      {gettext("Learn more")}
      <.icon
        name="hero-arrow-right-mini"
        class="ml-1 h-4 w-4 transition-transform duration-150 ease-in-out group-hover:translate-x-0.5"
      />
    </.button>
  </:action_buttons>
  <:description>
    {gettext(
      "Adapt this landing page template to your own web application. Now you can make your next great web app in record time."
    )}
  </:description>
  <:cloud_logo>
    <svg
      class="fade-in-animation mx-auto h-6"
      xmlns="http://www.w3.org/2000/svg"
      width="127"
      height="32"
      viewBox="0 0 127 32"
      fill="none"
    >
      <g clip-path="url(#clip0)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M40.458 25.1963L42.0363 21.5289C43.741 22.8036 46.0122 23.4663 48.2531 23.4663C49.9073 23.4663 50.9544 22.8289 50.9544 21.8628C50.9291 19.1615 41.0448 21.276 40.9689 14.5027C40.9436 11.0629 43.999 8.41225 48.329 8.41225C50.9038 8.41225 53.4735 9.04962 55.3097 10.5014L53.8327 14.2447C52.1532 13.1723 50.0641 12.4085 48.0761 12.4085C46.7255 12.4085 45.8352 13.0458 45.8352 13.8602C45.8605 16.5109 55.8206 15.0591 55.9218 21.5289C55.9218 25.0446 52.9424 27.5182 48.6629 27.5182C45.5266 27.5131 42.6483 26.7746 40.458 25.1963ZM101.064 20.2036C100.275 21.5795 98.7976 22.5204 97.0878 22.5204C94.5636 22.5204 92.5301 20.4818 92.5301 17.9627C92.5301 15.4435 94.5687 13.405 97.0878 13.405C98.7925 13.405 100.275 14.3459 101.064 15.7218L105.419 13.3038C103.79 10.4002 100.654 8.41225 97.0878 8.41225C91.8118 8.41225 87.5323 12.6917 87.5323 17.9677C87.5323 23.2437 91.8118 27.5232 97.0878 27.5232C100.679 27.5232 103.79 25.5605 105.419 22.6317L101.064 20.2036ZM58.1121 0.485596H63.5652V27.159H58.1121V0.485596ZM107.559 0.485596V27.159H113.012V19.1565L119.482 27.159H126.462L118.232 17.6541L125.85 8.78658H119.173L113.007 16.1467V0.485596H107.559Z"
          fill="#758A99"
        />
        <path
          d="M79.7675 20.2542C78.9784 21.5542 77.3495 22.5204 75.5133 22.5204C72.9891 22.5204 70.9556 20.4818 70.9556 17.9627C70.9556 15.4436 72.9942 13.405 75.5133 13.405C77.3495 13.405 78.9784 14.4218 79.7675 15.7471V20.2542ZM79.7675 8.79167V10.9567C78.8772 9.45433 76.6616 8.40723 74.3397 8.40723C69.5493 8.40723 65.7808 12.6361 65.7808 17.9374C65.7808 23.2387 69.5493 27.5182 74.3397 27.5182C76.6565 27.5182 78.8721 26.4762 79.7675 24.9687V27.1338H85.2205V8.79167H79.7675Z"
          fill="#758A99"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.75318 20.2289C6.75318 22.0904 5.25081 23.5928 3.38929 23.5928C1.52776 23.5928 0.0253906 22.0854 0.0253906 20.2289C0.0253906 18.3724 1.52776 16.865 3.38929 16.865H6.75318V20.2289ZM8.4326 20.2289C8.4326 18.3674 9.93497 16.865 11.7965 16.865C13.658 16.865 15.1604 18.3674 15.1604 20.2289V28.6361C15.1604 30.4976 13.658 32 11.7965 32C9.93497 32 8.4326 30.4976 8.4326 28.6361V20.2289Z"
          fill="#758A99"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M11.7964 6.72779C9.93487 6.72779 8.4325 5.22542 8.4325 3.3639C8.4325 1.50237 9.93487 0 11.7964 0C13.6579 0 15.1603 1.50237 15.1603 3.3639V6.72779H11.7964ZM11.7964 8.4325C13.6579 8.4325 15.1603 9.93487 15.1603 11.7964C15.1603 13.6579 13.6579 15.1603 11.7964 15.1603H3.3639C1.50237 15.1603 0 13.6529 0 11.7964C0 9.93993 1.50237 8.4325 3.3639 8.4325H11.7964Z"
          fill="#758A99"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M25.2724 11.7964C25.2724 9.93487 26.7748 8.4325 28.6363 8.4325C30.4979 8.4325 32.0002 9.93487 32.0002 11.7964C32.0002 13.6579 30.4979 15.1603 28.6363 15.1603H25.2724V11.7964ZM23.593 11.7964C23.593 13.6579 22.0907 15.1603 20.2291 15.1603C18.3676 15.1603 16.8652 13.6579 16.8652 11.7964V3.3639C16.8652 1.50237 18.3676 0 20.2291 0C22.0907 0 23.593 1.50237 23.593 3.3639V11.7964Z"
          fill="#758A99"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M20.2291 25.2722C22.0907 25.2722 23.593 26.7746 23.593 28.6361C23.593 30.4976 22.0907 32 20.2291 32C18.3676 32 16.8652 30.4976 16.8652 28.6361V25.2722H20.2291ZM20.2291 23.5928C18.3676 23.5928 16.8652 22.0904 16.8652 20.2289C16.8652 18.3674 18.3676 16.865 20.2291 16.865H28.6616C30.5232 16.865 32.0255 18.3674 32.0255 20.2289C32.0255 22.0904 30.5232 23.5928 28.6616 23.5928H20.2291Z"
          fill="#758A99"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect width="126.462" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </:cloud_logo>
  <:cloud_logo>
    <svg
      class="fade-in-animation mx-auto h-6"
      xmlns="http://www.w3.org/2000/svg"
      width="160"
      height="32"
      viewBox="0 0 160 32"
      fill="none"
    >
      <g clip-path="url(#clip0)">
        <path
          d="M18.4939 5.91684L9.24696 11.8192L18.4939 17.7215L9.24696 23.6238L0 17.6723L9.24696 11.77L0 5.91684L9.24696 0.0145264L18.4939 5.91684ZM9.19778 25.4929L18.4447 19.5905L27.6917 25.4929L18.4447 31.3952L9.19778 25.4929ZM18.4939 17.6723L27.7409 11.77L18.4939 5.91684L27.6917 0.0145264L36.9387 5.91684L27.6917 11.8192L36.9387 17.7215L27.6917 23.6238L18.4939 17.6723Z"
          fill="#758A99"
        />
        <path
          d="M44.1689 5.91685H51.6452C56.4163 5.91685 60.3511 8.67126 60.3511 14.9671V16.2951C60.3511 22.6401 56.6622 25.4929 51.7928 25.4929H44.1689V5.91685ZM48.3498 9.45824V21.9023H51.5468C54.2521 21.9023 56.0719 20.1316 56.0719 16.1967V15.1638C56.0719 11.2289 54.1537 9.45824 51.3993 9.45824H48.3498ZM62.5645 9.65498H65.9091L66.4502 13.3439C67.0896 10.8354 68.7127 9.50742 71.6639 9.50742H72.6968V13.7374H70.9753C67.5815 13.7374 66.7453 14.9179 66.7453 18.2625V25.5421H62.6137V9.65498H62.5645ZM73.533 17.9182V17.4756C73.533 12.1635 76.9268 9.26149 81.5503 9.26149C86.2721 9.26149 89.5676 12.1635 89.5676 17.4756V17.9182C89.5676 23.1319 86.3705 25.9355 81.5503 25.9355C76.4349 25.8864 73.533 23.1319 73.533 17.9182ZM85.3376 17.869V17.4756C85.3376 14.5244 83.862 12.7537 81.5011 12.7537C79.1894 12.7537 77.6646 14.3768 77.6646 17.4756V17.869C77.6646 20.7218 79.1402 22.345 81.5011 22.345C83.862 22.2958 85.3376 20.7218 85.3376 17.869ZM91.7318 9.65498H95.1748L95.5683 12.6553C96.4044 10.6387 98.1751 9.26149 100.782 9.26149C104.815 9.26149 107.471 12.1635 107.471 17.5247V17.9674C107.471 23.1811 104.52 25.9355 100.782 25.9355C98.2735 25.9355 96.552 24.8043 95.7158 22.9844V31.0017H91.6826L91.7318 9.65498ZM103.29 17.869V17.5247C103.29 14.3768 101.667 12.8029 99.5032 12.8029C97.1914 12.8029 95.6667 14.5736 95.6667 17.5247V17.8199C95.6667 20.6235 97.1422 22.3941 99.454 22.3941C101.815 22.345 103.29 20.8202 103.29 17.869ZM113.57 22.5909L113.226 25.4929H109.685V4.3429H113.718V12.4586C114.603 10.3928 116.374 9.26149 118.882 9.26149C122.67 9.31068 125.473 11.9175 125.473 17.1804V17.6723C125.473 22.9352 122.817 25.9355 118.784 25.9355C116.128 25.8864 114.407 24.6567 113.57 22.5909ZM121.243 17.6723V17.2788C121.243 14.3768 119.669 12.7537 117.456 12.7537C115.193 12.7537 113.62 14.5736 113.62 17.328V17.6723C113.62 20.6235 115.144 22.345 117.407 22.345C119.817 22.345 121.243 20.8202 121.243 17.6723ZM127.244 17.9182V17.4756C127.244 12.1635 130.638 9.26149 135.261 9.26149C139.983 9.26149 143.279 12.1635 143.279 17.4756V17.9182C143.279 23.1319 140.032 25.9355 135.261 25.9355C130.146 25.8864 127.244 23.1319 127.244 17.9182ZM139.098 17.869V17.4756C139.098 14.5244 137.622 12.7537 135.261 12.7537C132.95 12.7537 131.425 14.3768 131.425 17.4756V17.869C131.425 20.7218 132.9 22.345 135.261 22.345C137.622 22.2958 139.098 20.7218 139.098 17.869ZM148.689 17.2788L143.131 9.65498H147.902L151.099 14.426L154.346 9.65498H159.067L153.411 17.2296L159.362 25.4929H154.69L151.05 20.23L147.509 25.4929H142.639L148.689 17.2788Z"
          fill="#758A99"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect width="159.363" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </:cloud_logo>
  <:cloud_logo>
    <svg
      class="fade-in-animation mx-auto h-6"
      xmlns="http://www.w3.org/2000/svg"
      width="107"
      height="32"
      viewBox="0 0 107 32"
      fill="none"
    >
      <g clip-path="url(#clip0)">
        <path
          d="M15.9999 0C7.16359 0 0 7.16339 0 15.9997C0 24.837 7.16359 32 15.9999 32C24.8366 32 31.9998 24.837 31.9998 15.9997C31.9998 7.16396 24.837 0.000763653 15.9997 0.000763653L15.9999 0ZM23.3374 23.0762C23.05 23.5475 22.4359 23.6952 21.9661 23.408C18.2089 21.1117 13.4798 20.593 7.91043 21.8652C7.37374 21.9883 6.83878 21.6518 6.71651 21.1153C6.59365 20.5786 6.92857 20.0437 7.4666 19.9214C13.5614 18.528 18.7894 19.1277 23.0066 21.7047C23.4766 21.9933 23.6256 22.6066 23.3372 23.0766L23.3374 23.0762ZM25.2957 18.7202C24.9338 19.3073 24.1662 19.4915 23.5798 19.131C19.28 16.4879 12.7234 15.7223 7.63646 17.2664C6.97672 17.4657 6.27993 17.0939 6.0797 16.4353C5.881 15.7756 6.253 15.0801 6.91158 14.8795C12.7223 13.1164 19.9458 13.9705 24.8847 17.0055C25.4713 17.3666 25.6566 18.1346 25.2955 18.7204L25.2957 18.7202ZM25.4638 14.1837C20.3064 11.1208 11.7992 10.8392 6.87583 12.3335C6.08524 12.5732 5.24916 12.1269 5.00957 11.3363C4.76998 10.5453 5.21591 9.70983 6.00709 9.46948C11.6588 7.75377 21.0542 8.08525 26.9912 11.6097C27.7023 12.0318 27.9356 12.9502 27.5141 13.6604C27.0938 14.3715 26.1728 14.6059 25.464 14.1837H25.4638ZM43.5304 14.7706C40.7679 14.1118 40.2761 13.6495 40.2761 12.6779C40.2761 11.7601 41.1403 11.1424 42.4253 11.1424C43.6711 11.1424 44.9062 11.6115 46.2013 12.5771C46.2404 12.6063 46.2895 12.6179 46.3379 12.6103C46.3862 12.6031 46.429 12.5765 46.4573 12.5366L47.8062 10.6351C47.8616 10.5568 47.8465 10.4489 47.7718 10.389C46.2305 9.15232 44.4949 8.55105 42.4663 8.55105C39.4834 8.55105 37.3999 10.3411 37.3999 12.9023C37.3999 15.6487 39.1972 16.6212 42.3031 17.3719C44.9466 17.9808 45.3927 18.4909 45.3927 19.4029C45.3927 20.4134 44.4905 21.0416 43.0387 21.0416C41.4261 21.0416 40.1109 20.4984 38.6395 19.2242C38.603 19.1927 38.553 19.1784 38.5067 19.181C38.458 19.1851 38.4135 19.2076 38.3825 19.2449L36.8701 21.0448C36.8067 21.1195 36.8147 21.2311 36.8881 21.2957C38.6 22.824 40.7054 23.6312 42.9775 23.6312C46.1917 23.6312 48.2687 21.875 48.2687 19.1568C48.2745 16.8622 46.9008 15.5916 43.5362 14.7731L43.5304 14.7706ZM55.5404 12.0461C54.1472 12.0461 53.0045 12.5948 52.0622 13.7192V12.4536C52.0622 12.3537 51.9812 12.2723 51.8815 12.2723H49.4078C49.3079 12.2723 49.2271 12.3537 49.2271 12.4536V26.516C49.2271 26.6159 49.3079 26.6973 49.4078 26.6973H51.8815C51.9812 26.6973 52.0622 26.6159 52.0622 26.516V22.0771C53.0047 23.1348 54.1476 23.6516 55.5404 23.6516C58.1291 23.6516 60.7497 21.6589 60.7497 17.8495C60.7535 14.0392 58.1322 12.0459 55.5433 12.0459L55.5404 12.0461ZM57.8733 17.8495C57.8733 19.7894 56.6784 21.143 54.9674 21.143C53.276 21.143 52.0001 19.7279 52.0001 17.8495C52.0001 15.9714 53.276 14.5561 54.9674 14.5561C56.6507 14.5559 57.8735 15.9407 57.8735 17.8493L57.8733 17.8495ZM67.4664 12.0461C64.1326 12.0461 61.5208 14.6132 61.5208 17.891C61.5208 21.1331 64.1146 23.6732 67.4255 23.6732C70.7711 23.6732 73.391 21.1147 73.391 17.8495C73.391 14.5956 70.7887 12.0465 67.4662 12.0465L67.4664 12.0461ZM67.4664 21.1623C65.6932 21.1623 64.3563 19.7376 64.3563 17.8486C64.3563 15.9515 65.6469 14.575 67.4255 14.575C69.2102 14.575 70.556 15.9997 70.556 17.89C70.556 19.7867 69.2568 21.1623 67.4666 21.1623H67.4664ZM80.51 12.2723H77.788V9.48935C77.788 9.38962 77.7074 9.30823 77.6074 9.30823H75.1342C75.0341 9.30823 74.9529 9.38962 74.9529 9.48935V12.2723H73.7635C73.664 12.2723 73.5833 12.3537 73.5833 12.4536V14.5796C73.5833 14.6793 73.664 14.7609 73.7635 14.7609H74.9529V20.2617C74.9529 22.4846 76.0593 23.6117 78.2414 23.6117C79.1287 23.6117 79.8648 23.4285 80.5586 23.0351C80.6149 23.0036 80.6499 22.9428 80.6499 22.8782V20.8538C80.6499 20.7913 80.6174 20.7323 80.5639 20.6996C80.51 20.6658 80.443 20.6641 80.3877 20.6914C79.9112 20.9312 79.4506 21.0418 78.9357 21.0418C78.142 21.0418 77.788 20.6814 77.788 19.8738V14.7618H80.51C80.6099 14.7618 80.6904 14.6805 80.6904 14.5805V12.4548C80.6942 12.3549 80.614 12.2735 80.5127 12.2735L80.51 12.2723ZM89.9942 12.2832V11.9414C89.9942 10.9359 90.3798 10.4874 91.2445 10.4874C91.7602 10.4874 92.1744 10.5898 92.6383 10.7446C92.6954 10.7626 92.755 10.7536 92.8015 10.7194C92.8492 10.6852 92.8762 10.6304 92.8762 10.5723V8.48781C92.8762 8.40813 92.825 8.33764 92.7482 8.31413C92.2581 8.16835 91.631 8.01875 90.6922 8.01875C88.4075 8.01875 87.1998 9.30535 87.1998 11.7381V12.2616H86.0114C85.9117 12.2616 85.8299 12.343 85.8299 12.4427V14.5796C85.8299 14.6793 85.9117 14.7609 86.0114 14.7609H87.1998V23.2456C87.1998 23.3455 87.2804 23.4269 87.3802 23.4269H89.8538C89.9537 23.4269 90.0351 23.3455 90.0351 23.2456V14.7615H92.3447L95.8825 23.2441C95.4809 24.1354 95.086 24.3127 94.5468 24.3127C94.111 24.3127 93.6521 24.1826 93.1828 23.9258C93.1387 23.9015 93.0863 23.8975 93.0388 23.9122C92.9916 23.9289 92.9518 23.964 92.9318 24.0099L92.0934 25.8492C92.0535 25.9361 92.0877 26.0382 92.1714 26.0833C93.0466 26.5573 93.8367 26.7596 94.8132 26.7596C96.6397 26.7596 97.6492 25.9088 98.5392 23.6199L102.831 12.5308C102.852 12.475 102.846 12.412 102.811 12.3625C102.777 12.3134 102.723 12.2838 102.663 12.2838H100.088C100.01 12.2838 99.9413 12.3329 99.9161 12.4053L97.2782 19.9403L94.3888 12.4003C94.3625 12.3302 94.2948 12.2838 94.2199 12.2838H89.9937L89.9942 12.2832ZM84.4955 12.2723H82.0219C81.922 12.2723 81.8406 12.3537 81.8406 12.4536V23.2456C81.8406 23.3456 81.922 23.4269 82.0219 23.4269H84.4955C84.5953 23.4269 84.6768 23.3456 84.6768 23.2456V12.4544C84.6769 12.4306 84.6722 12.4069 84.6631 12.3849C84.654 12.3629 84.6406 12.3428 84.6238 12.326C84.6069 12.3091 84.5869 12.2958 84.5648 12.2867C84.5428 12.2776 84.5192 12.273 84.4953 12.2731L84.4955 12.2723ZM83.2727 7.35847C82.2928 7.35847 81.4976 8.15174 81.4976 9.13169C81.4976 10.1122 82.293 10.9065 83.2728 10.9065C84.2523 10.9065 85.0467 10.1122 85.0467 9.13169C85.0467 8.15193 84.2519 7.35847 83.2737 7.35847H83.2727ZM104.943 15.7422C103.964 15.7422 103.202 14.9559 103.202 14.0012C103.202 13.0465 103.974 12.2513 104.952 12.2513C105.931 12.2513 106.693 13.0373 106.693 13.9913C106.693 14.946 105.921 15.7422 104.943 15.7422H104.943ZM104.952 12.4244C104.061 12.4244 103.386 13.1332 103.386 14.0012C103.386 14.8688 104.056 15.5681 104.943 15.5681C105.835 15.5681 106.51 14.8599 106.51 13.9913C106.51 13.1237 105.84 12.4244 104.953 12.4244H104.952ZM105.338 14.1703L105.831 14.8597H105.415L104.972 14.2272H104.591V14.8597H104.243V13.0324H105.058C105.483 13.0324 105.762 13.2496 105.762 13.6153C105.764 13.9149 105.59 14.0979 105.34 14.1703H105.338V14.1703ZM105.044 13.3459H104.591V13.9239H105.044C105.27 13.9239 105.405 13.8132 105.405 13.6346C105.405 13.4466 105.27 13.3459 105.044 13.3459H105.044V13.3459Z"
          fill="#758A99"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect width="106.693" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </:cloud_logo>
  <:cloud_logo>
    <svg
      class="fade-in-animation mx-auto h-6"
      xmlns="http://www.w3.org/2000/svg"
      width="107"
      height="32"
      viewBox="0 0 107 32"
      fill="none"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M66.6436 25.0257C60.4897 29.5759 51.5387 32.0001 43.8184 32.0001C33.0026 32.0001 23.3056 28.0094 15.921 21.3707C15.3615 20.8485 15.8464 20.1399 16.555 20.5501C24.4991 25.1749 34.3079 27.9348 44.4524 27.9348C51.2776 27.9348 58.8114 26.5176 65.7485 23.5711C66.7928 23.1609 67.6506 24.2798 66.6436 25.0257Z"
        fill="#758A99"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M69.2169 22.0792C68.4337 21.0722 63.9955 21.5944 62.0188 21.8555C61.422 21.9301 61.3102 21.4079 61.8696 21.0349C65.4127 18.5361 71.1936 19.282 71.8649 20.1025C72.5363 20.9231 71.6784 26.7412 68.3591 29.5012C67.837 29.9114 67.3521 29.6876 67.5759 29.1282C68.3218 27.2634 70.0001 23.1235 69.2169 22.0792Z"
        fill="#758A99"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M62.1307 3.50582V1.08158C62.1307 0.708622 62.3917 0.484844 62.7274 0.484844H73.5433C73.8789 0.484844 74.1773 0.745918 74.1773 1.08158V3.13286C74.1773 3.46853 73.8789 3.91608 73.3568 4.662L67.7624 12.6434C69.851 12.6061 72.0514 12.9044 73.9162 13.9487C74.3265 14.1725 74.4384 14.5455 74.4757 14.8811V17.4545C74.4757 17.8275 74.1027 18.2378 73.6925 18.014C70.3731 16.2611 65.9349 16.0746 62.2799 18.0513C61.9069 18.2378 61.4966 17.8648 61.4966 17.4918V15.0303C61.4966 14.6573 61.4966 13.986 61.9069 13.3893L68.3964 4.10256H62.7647C62.429 4.10256 62.1307 3.84149 62.1307 3.50582ZM22.7088 18.5361H19.4267C19.1283 18.4988 18.8673 18.275 18.83 17.9767V1.11888C18.83 0.783217 19.1283 0.522142 19.464 0.522142H22.5223C22.8579 0.522142 23.0817 0.783214 23.119 1.08158V3.28205H23.1936C23.9768 1.15617 25.506 0.149182 27.52 0.149182C29.5712 0.149182 30.8766 1.15617 31.7717 3.28205C32.5549 1.15617 34.3824 0.149182 36.3218 0.149182C37.7018 0.149182 39.1936 0.708622 40.126 2.01398C41.1703 3.43123 40.9465 5.48252 40.9465 7.31002V17.9767C40.9465 18.3123 40.6482 18.5734 40.3125 18.5734H37.0677C36.7321 18.5361 36.471 18.275 36.471 17.9767V9.02563C36.471 8.31701 36.5456 6.5268 36.3964 5.85547C36.1353 4.73659 35.4267 4.40093 34.457 4.40093C33.6738 4.40093 32.816 4.92308 32.4803 5.78089C32.1447 6.63869 32.182 8.05594 32.182 9.02563V17.9767C32.182 18.3123 31.8836 18.5734 31.5479 18.5734H28.2659C27.9302 18.5361 27.6691 18.275 27.6691 17.9767V9.02563C27.6691 7.16083 27.9675 4.36363 25.6551 4.36363C23.3055 4.36363 23.3801 7.04894 23.3801 9.02563V17.9767C23.3428 18.275 23.0817 18.5361 22.7088 18.5361ZM83.5013 0.149182C88.3871 0.149182 91.0351 4.32634 91.0351 9.65967C91.0351 14.8065 88.126 18.9091 83.5013 18.9091C78.7274 18.9091 76.1167 14.7319 76.1167 9.51049C76.0794 4.25175 78.7274 0.149182 83.5013 0.149182ZM83.5013 3.61771C81.0771 3.61771 80.9279 6.93706 80.9279 8.98835C80.9279 11.0396 80.8906 15.4406 83.464 15.4406C86.0001 15.4406 86.1493 11.8974 86.1493 9.73427C86.1493 8.31702 86.0747 6.6014 85.6645 5.25874C85.2915 4.06527 84.5456 3.61771 83.5013 3.61771ZM97.3381 18.5361H94.0561C93.7204 18.4988 93.4594 18.2378 93.4594 17.9394V1.04428C93.4967 0.745916 93.7577 0.484844 94.0934 0.484844H97.1517C97.45 0.484844 97.6738 0.708627 97.7484 0.969699V3.54312H97.823C98.7554 1.23077 100.023 0.149182 102.299 0.149182C103.753 0.149182 105.208 0.671322 106.14 2.12587C106.998 3.46852 106.998 5.74358 106.998 7.38461V18.014C106.961 18.3123 106.699 18.5361 106.364 18.5361H103.082C102.783 18.4988 102.522 18.2751 102.485 18.014V8.83915C102.485 6.97435 102.709 4.28904 100.434 4.28904C99.6505 4.28904 98.9046 4.81119 98.5316 5.6317C98.0841 6.67599 98.0095 7.68298 98.0095 8.83915V17.9394C97.9722 18.2751 97.6738 18.5361 97.3381 18.5361ZM53.5153 10.4802C53.5153 11.7483 53.5526 12.8298 52.9186 13.986C52.3964 14.9184 51.5759 15.4779 50.6435 15.4779C49.3754 15.4779 48.6295 14.5082 48.6295 13.0909C48.6295 10.2937 51.1283 9.77155 53.5153 9.77155V10.4802ZM56.8346 18.4988C56.6109 18.6853 56.3125 18.7226 56.0514 18.5734C54.9698 17.6783 54.7461 17.2308 54.1493 16.373C52.3591 18.2005 51.0537 18.7599 48.7414 18.7599C45.9815 18.7599 43.8183 17.0443 43.8183 13.6504C43.8183 10.965 45.2729 9.17482 47.3242 8.27971C49.1144 7.4965 51.6132 7.34732 53.5153 7.12354V6.71329C53.5153 5.93007 53.5899 4.99767 53.105 4.32634C52.6948 3.72961 51.9489 3.46853 51.2775 3.46853C50.0095 3.46853 48.8906 4.10257 48.6295 5.44522C48.5549 5.74359 48.3684 6.04195 48.0701 6.04195L44.8999 5.70629C44.6388 5.6317 44.3405 5.44522 44.4151 5.03496C45.161 1.15618 48.6295 0 51.7624 0C53.3661 0 55.4547 0.410254 56.7228 1.64102C58.3265 3.13286 58.1773 5.14685 58.1773 7.31002V12.4196C58.1773 13.9487 58.8113 14.62 59.4081 15.4779C59.6318 15.7762 59.6691 16.1492 59.4081 16.3357C58.6994 16.8951 57.506 17.9394 56.8346 18.4988ZM10.4384 10.4802C10.4384 11.7483 10.4757 12.8298 9.84163 13.986C9.31949 14.9184 8.49897 15.4779 7.56657 15.4779C6.29851 15.4779 5.55259 14.5082 5.55259 13.0909C5.55259 10.2937 8.05142 9.77155 10.4384 9.77155V10.4802ZM13.7204 18.4988C13.4966 18.6853 13.1983 18.7226 12.9372 18.5734C11.8556 17.6783 11.6318 17.2308 11.0351 16.373C9.24489 18.2005 7.93953 18.7599 5.62718 18.7599C2.86727 18.7599 0.704102 17.0443 0.704102 13.6504C0.704102 10.965 2.15864 9.17482 4.20993 8.27971C6.00014 7.4965 8.49897 7.34732 10.4011 7.12354V6.71329C10.4011 5.93007 10.4757 4.99767 9.99081 4.32634C9.58056 3.72961 8.83464 3.46853 8.16331 3.46853C6.89524 3.46853 5.77636 4.10257 5.51529 5.44522C5.4407 5.74359 5.25422 6.04195 4.95585 6.04195L1.78568 5.70629C1.52461 5.6317 1.22625 5.44522 1.30084 5.03496C2.04676 1.15618 5.51529 0 8.64816 0C10.2519 0 12.3405 0.410254 13.6085 1.64102C15.2123 3.13286 15.0631 5.14685 15.0631 7.31002V12.4196C15.0631 13.9487 15.6971 14.62 16.2938 15.4779C16.5176 15.7762 16.5549 16.1492 16.2938 16.3357C15.5852 16.8951 14.3917 17.9394 13.7204 18.4988Z"
        fill="#758A99"
      />
    </svg>
  </:cloud_logo>
  <:cloud_logo>
    <svg
      class="fade-in-animation mx-auto h-6"
      xmlns="http://www.w3.org/2000/svg"
      width="119"
      height="32"
      viewBox="0 0 119 32"
      fill="none"
    >
      <g clip-path="url(#clip0)">
        <path
          d="M16.2725 29.93C14.4923 30.2426 12.6808 30.3363 10.8068 30.5859L5.09095 13.845V31.3044C3.31072 31.4918 1.68662 31.7416 0 31.9915V0.00854492H4.7476L11.244 18.1554V0.00854492H16.2725V29.93ZM26.1111 11.7211C28.0477 11.7211 31.0148 11.6273 32.7951 11.6273V16.6246C30.5774 16.6246 27.9852 16.6246 26.1111 16.7183V24.1519C29.0471 23.9646 31.9831 23.7145 34.9501 23.6208V28.4304L21.1136 29.5238V0.00854492H34.9501V5.00589H26.1111V11.7211ZM53.5339 5.006H48.3492V27.9937C46.6626 27.9937 44.976 27.9937 43.3521 28.056V5.006H38.1674V0.00854492H53.5341L53.5339 5.006ZM61.6545 11.3776H68.4947V16.3748H61.6545V27.7126H56.7505V0.00854492H70.7123V5.00589H61.6545V11.3776ZM78.8329 23.3398C81.6752 23.4021 84.5485 23.6211 87.3284 23.777V28.712C82.8621 28.4307 78.3957 28.15 73.8356 28.056V0.00854492H78.8329V23.3398ZM91.5448 29.0556C93.1377 29.1494 94.8243 29.2431 96.4484 29.4302V0.00854492H91.5448V29.0556ZM118.343 0.00854492L112.003 15.2192L118.343 31.9915C116.469 31.7416 114.595 31.398 112.721 31.0856L109.129 21.8408L105.475 30.3363C103.663 30.0237 101.914 29.93 100.103 29.6801L106.537 15.0317L100.728 0.00854492H106.1L109.379 8.41033L112.877 0.00854492H118.343Z"
          fill="#758A99"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect width="118.343" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </:cloud_logo>
  <:cloud_logo>
    <svg
      class="fade-in-animation mx-auto h-6"
      xmlns="http://www.w3.org/2000/svg"
      width="77"
      height="32"
      viewBox="0 0 77 32"
      fill="none"
    >
      <g clip-path="url(#clip0)">
        <path
          d="M5.38008 12.484C5.38008 11.6547 6.06056 11.3356 7.1878 11.3356C8.80383 11.3356 10.8452 11.8249 12.4614 12.6967V7.69928C10.6965 6.99757 8.95275 6.72111 7.1878 6.72111C2.87081 6.72111 0 8.97529 0 12.7393C0 18.6084 8.08074 17.6728 8.08074 20.2035C8.08074 21.1815 7.23026 21.5006 6.03932 21.5006C4.27437 21.5006 2.02019 20.7775 0.233852 19.7993V24.8605C2.21157 25.7111 4.21053 26.0725 6.03932 26.0725C10.4625 26.0725 13.5034 23.8823 13.5034 20.0758C13.4822 13.7387 5.38008 14.8657 5.38008 12.484ZM19.7554 2.46812L14.5667 3.57383L14.5455 20.6073C14.5455 23.7547 16.906 26.0725 20.0532 26.0725C21.7969 26.0725 23.0728 25.7536 23.7745 25.3708V21.0539C23.0942 21.3304 19.7341 22.3086 19.7341 19.1613V11.6121H23.7745V7.08265H19.7341L19.7554 2.46812ZM30.3882 8.65621L30.0478 7.08265H25.4545V25.6897H30.7709V13.0795C32.0254 11.4421 34.1521 11.7398 34.8113 11.9736V7.08265C34.1308 6.82742 31.6428 6.35956 30.3882 8.65621ZM36.1084 7.08265H41.446V25.6897H36.1084V7.08265ZM36.1084 5.46647L41.446 4.31815V0.00130939L36.1084 1.1284V5.46632V5.46647ZM52.5465 6.72111C50.4625 6.72111 49.1228 7.69928 48.3786 8.3799L48.102 7.06142H43.4237V31.8566L48.74 30.7296L48.7614 24.7115C49.5269 25.2645 50.6539 26.0512 52.5253 26.0512C56.3318 26.0512 59.798 22.989 59.798 16.248C59.7768 10.081 56.2679 6.72111 52.5465 6.72111ZM51.2706 21.3729C50.016 21.3729 49.2715 20.9264 48.7614 20.3735L48.74 12.484C49.2929 11.8673 50.0585 11.4421 51.2706 11.4421C53.2057 11.4421 54.5455 13.6111 54.5455 16.3968C54.5455 19.2464 53.227 21.3729 51.2706 21.3729ZM76.555 16.4606C76.555 11.0167 73.9181 6.72111 68.8783 6.72111C63.817 6.72111 60.7548 11.0169 60.7548 16.4182C60.7548 22.8189 64.3701 26.0512 69.5586 26.0512C72.0893 26.0512 74.0031 25.4771 75.4492 24.6691V20.4159C74.0033 21.139 72.3445 21.5856 70.2392 21.5856C68.1764 21.5856 66.3476 20.8626 66.1138 18.3533H76.5126C76.5126 18.0767 76.555 16.971 76.555 16.4606ZM66.0499 14.4405C66.0499 12.0375 67.5173 11.0379 68.8571 11.0379C70.1542 11.0379 71.5365 12.0375 71.5365 14.4405H66.0499Z"
          fill="#6E7E95"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect width="76.555" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </:cloud_logo>
</LandingPageComponents.hero>

<LandingPageComponents.features
  title={gettext("Features")}
  description={gettext("Here are some features you can use to get started with your web app.")}
  features={[
    %{
      id: "1",
      title: "Authentication",
      description: Faker.Lorem.sentence(10..20),
      icon: "hero-bolt-solid"
    },
    %{
      id: "2",
      title: "HTML Emails",
      description: Faker.Lorem.sentence(10..20),
      icon: "hero-puzzle-piece-solid"
    },
    %{
      id: "3",
      title: "Wallaby testing",
      description: Faker.Lorem.sentence(10..20),
      icon: "hero-cube-solid"
    },
    %{
      id: "4",
      title: "Real-time Notifications",
      description: Faker.Lorem.sentence(10..20),
      icon: "hero-bell-solid"
    },
    %{
      id: "5",
      title: "Multi-tenancy",
      description: Faker.Lorem.sentence(10..20),
      icon: "hero-building-office-solid"
    },
    %{
      id: "6",
      title: "Stripe Billing",
      description: Faker.Lorem.sentence(10..20),
      icon: "hero-credit-card-solid"
    }
  ]}
  max_width={max_width}
/>

<LandingPageComponents.solo_feature
  title={gettext("Page builder")}
  icon="hero-cube-solid"
  description={
    gettext(
      "Build out your web application by navigating to unused routes and creating pages out of thin air!"
    )
  }
  image_src_light={~p"/images/landing_page/dashboard_light.png"}
  image_src_dark={~p"/images/landing_page/dashboard_dark.png"}
  max_width={max_width}
>
  <.button
    variant="solid"
    link_type="a"
    to="/test-page"
    label={gettext("Try it out now")}
    color="primary"
    size="lg"
    class="mt-8"
  />
</LandingPageComponents.solo_feature>

<LandingPageComponents.solo_feature
  inverted
  title={gettext("Layouts")}
  icon="hero-table-cells-solid"
  description={
    gettext(
      "Layouts can be tricky to build. Petal Pro makes it easy with it's stacked and sidebar layouts."
    )
  }
  image_src_light={~p"/images/landing_page/dashboard_light.png"}
  image_src_dark={~p"/images/landing_page/dashboard_dark.png"}
  blur_color="secondary"
  max_width={max_width}
/>

<LandingPageComponents.testimonials
  max_width={max_width}
  testimonials={
    [
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595191/dummy_data/avatar_32_cayiid.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595191/dummy_data/avatar_26_mjcxen.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595189/dummy_data/avatar_10_d3tw5q.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595190/dummy_data/avatar_17_eolat2.png",
      "https://res.cloudinary.com/wickedsites/image/upload/v1636595189/dummy_data/avatar_13_xrlvql.png"
    ]
    |> Enum.map(fn src ->
      %{
        name: Faker.Person.name(),
        title: "CEO, #{Faker.Company.name()}",
        image_src: src,
        content: Faker.Lorem.sentence(10..20)
      }
    end)
  }
/>

<LandingPageComponents.pricing
  title={gettext("Pricing")}
  description={gettext("Simple, transparent pricing that adapts to the size of your company.")}
  max_width={max_width}
/>
