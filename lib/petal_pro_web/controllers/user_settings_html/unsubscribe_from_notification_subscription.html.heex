<div class="fixed inset-0 z-10 overflow-y-auto">
  <div class="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 transition-opacity" aria-hidden="true">
      <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
    </div>

    <span class="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
      &#8203;
    </span>
    <div
      class="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-headline"
    >
      <div>
        <%= if Map.get(@user, @subscription.user_field) do %>
          <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
            <svg
              class="h-6 w-6 text-yellow-600"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>

          <div class="mt-3 text-center sm:mt-5">
            <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-headline">
              {gettext("Confirm unsubscribe from %{subscription}",
                subscription: @subscription.label
              )}
            </h3>
            <div class="prose-sm mt-2 text-sm text-gray-500">
              {@subscription.unsub_description} .
            </div>

            <%= if assigns[:current_user] do %>
              <div class="prose-sm mt-2 text-sm text-gray-500">
                {gettext("Or, manage your")}
                <.link
                  class="text-blue-500 hover:underline"
                  href={~p"/app/users/edit-notifications"}
                >
                  {gettext("notification preferences")}
                </.link>
              </div>
            <% end %>
          </div>

          <div class="mt-10 flex items-center justify-center gap-5">
            <.link href="/" class="text-sm text-gray-600 hover:underline">
              Cancel
            </.link>

            <.button
              link_type="a"
              to={~p"/unsubscribe/#{@code}/#{@subscription.name}"}
              label={gettext("Yes, unsubscribe")}
              method={:put}
            />
          </div>
        <% else %>
          <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            <svg
              class="h-6 w-6 text-green-600"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-5">
            <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-headline">
              {gettext("Unsubscribed from %{subscription}", subscription: @subscription.label)}
            </h3>
            <div class="prose-sm mt-2 text-sm text-gray-500">
              {@subscription.unsub_description}
            </div>

            <%= if assigns[:current_user] do %>
              <div class="prose-sm mt-2 text-sm text-gray-500">
                {gettext("Or, manage your")}
                <.link
                  href={~p"/app/users/edit-notifications"}
                  class="text-blue-500 hover:underline"
                >
                  <%= gettext("notification preferences") %>
                </.link>.
              </div>
            <% end %>
          </div>

          <div class="mt-10 flex items-center justify-center gap-5">
            <.link
              method="put"
              href={~p"/unsubscribe/#{@code}/#{@subscription.name}"}
              class="text-sm text-gray-600 hover:underline"
            >
              {gettext("Undo")}
            </.link>
            <.button to="/" link_type="a" label={gettext("Continue")} />
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
