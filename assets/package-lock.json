{"name": "myapp_assets", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "myapp_assets", "version": "0.1.0", "dependencies": {"@alpinejs/persist": "^3.13.10", "@editorjs/code": "^2.9.2", "@editorjs/delimiter": "^1.4.2", "@editorjs/editorjs": "^2.30.6", "@editorjs/embed": "^2.7.6", "@editorjs/header": "^2.8.7", "@editorjs/inline-code": "^1.5.1", "@editorjs/list": "^1.10.0", "@editorjs/marker": "^1.4.0", "@editorjs/quote": "^2.7.2", "@editorjs/simple-image": "^1.6.0", "@editorjs/table": "^2.4.1", "@editorjs/warning": "^1.4.0", "@floating-ui/dom": "^1.6.5", "alpinejs": "^3.13.10", "chart.js": "^4.4.3", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-autocolors": "^0.2.2", "luxon": "^3.4.4", "phoenix": "file:../deps/phoenix", "phoenix_html": "file:../deps/phoenix_html", "phoenix_live_view": "file:../deps/phoenix_live_view", "tippy.js": "^6.3.7", "tom-select": "^2.3.1"}, "devDependencies": {"esbuild": "^0.12.24"}}, "../deps/phoenix": {"version": "1.7.20", "license": "MIT"}, "../deps/phoenix_html": {"version": "4.2.1"}, "../deps/phoenix_live_view": {"version": "1.0.5", "license": "MIT", "dependencies": {"morphdom": "2.7.4"}, "devDependencies": {"@babel/cli": "7.26.4", "@babel/core": "7.26.0", "@babel/preset-env": "7.26.0", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@stylistic/eslint-plugin-js": "^2.12.1", "css.escape": "^1.5.1", "eslint": "9.18.0", "eslint-plugin-jest": "28.10.0", "eslint-plugin-playwright": "^2.1.0", "globals": "^15.14.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-monocart-coverage": "^1.1.1", "monocart-reporter": "^2.9.13", "phoenix": "1.7.18"}}, "node_modules/@alpinejs/persist": {"version": "3.13.10", "license": "MIT"}, "node_modules/@codexteam/icons": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.5.tgz", "integrity": "sha512-s6H2KXhLz2rgbMZSkRm8dsMJvyUNZsEjxobBEg9ztdrb1B2H3pEzY6iTwI4XUPJWJ3c3qRKwV4TrO3J5jUdoQA=="}, "node_modules/@editorjs/code": {"version": "2.9.2", "resolved": "https://registry.npmjs.org/@editorjs/code/-/code-2.9.2.tgz", "integrity": "sha512-KglaZMeL9fREMieyMJ5dWZR+eGPC/6qjWRc2EmwdwcuCsQVuQ1k7sgPzRL3phJWrtDqPvzNKJqjEji/OD7SF+Q==", "dependencies": {"@codexteam/icons": "^0.3.2"}}, "node_modules/@editorjs/code/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/delimiter": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/@editorjs/delimiter/-/delimiter-1.4.2.tgz", "integrity": "sha512-S8q2LpeYdYkVShLp7K8c4HLthDHBevLw+sT+iO0+SH0oMvFmld9SUon3DFzMQ2gG07EOdZGRZ958+sVxyvFjZw==", "dependencies": {"@codexteam/icons": "^0.3.2"}}, "node_modules/@editorjs/delimiter/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/dom": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@editorjs/dom/-/dom-0.0.5.tgz", "integrity": "sha512-SZ78Gwpkp3EUhjBIp0lSojeQ35V9acF8SubJsMeOH/vlOUE40GOnvvwWZnF05lO7bIB0dOHhhJy4N7IIAWxP2w==", "dependencies": {"@editorjs/helpers": "^0.0.4"}}, "node_modules/@editorjs/editorjs": {"version": "2.30.6", "resolved": "https://registry.npmjs.org/@editorjs/editorjs/-/editorjs-2.30.6.tgz", "integrity": "sha512-6eQMc4Di3Hz9p4o+NGRgKaCeAF7eAk106m+bsDLc4eo94VGYO1M163OiGFdmanE+w503qTmXOzycWff5blEOAQ=="}, "node_modules/@editorjs/embed": {"version": "2.7.6", "resolved": "https://registry.npmjs.org/@editorjs/embed/-/embed-2.7.6.tgz", "integrity": "sha512-L3agW/23mOI0L+oksUE9UOR5VSNCqapxLH5lma+5j+idjKCC31nxbx07x53MSJ4rlOTO1L7cFVhkqptEdOliJA==", "dependencies": {"@editorjs/editorjs": "^2.29.1"}}, "node_modules/@editorjs/header": {"version": "2.8.7", "resolved": "https://registry.npmjs.org/@editorjs/header/-/header-2.8.7.tgz", "integrity": "sha512-rfxzYFR/Jhaocj3Xxx8XjEjyzfPbBIVkcPZ9Uy3rEz1n3ewhV0V4zwuxCjVfFhLUVgQQExq43BxJnTNlLOzqDQ==", "dependencies": {"@codexteam/icons": "^0.0.5", "@editorjs/editorjs": "^2.29.1"}}, "node_modules/@editorjs/helpers": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/@editorjs/helpers/-/helpers-0.0.4.tgz", "integrity": "sha512-ieg3dzo2m1/ELze/RMNADiAiC5amXxIlVXoJ5vvXITOu/p/dPsrF+Oi3h5gBYvtGk9vg5LJUSG5YWU0tBUO1tw=="}, "node_modules/@editorjs/inline-code": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/@editorjs/inline-code/-/inline-code-1.5.1.tgz", "integrity": "sha512-XvKpqw9y1bOYgyuVUHGDuu7KlNWCYIXD5uU1Lpc4s4LJ8VN4TjWFGKJ3sS+LR7zzasWe8hu2ffR1JC6MHIS0EQ==", "dependencies": {"@codexteam/icons": "^0.3.2"}}, "node_modules/@editorjs/inline-code/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/list": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/@editorjs/list/-/list-1.10.0.tgz", "integrity": "sha512-zXCHaNcIscpefnteBOS3x+98f/qBgEVsv+OvtKoTDZipMNqck2uVG+X2qMQr8xcwtJrj9ySX54lUac9FDlAHnA==", "dependencies": {"@codexteam/icons": "^0.0.4"}}, "node_modules/@editorjs/list/node_modules/@codexteam/icons": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.4.tgz", "integrity": "sha512-V8N/TY2TGyas4wLrPIFq7bcow68b3gu8DfDt1+rrHPtXxcexadKauRJL6eQgfG7Z0LCrN4boLRawR4S9gjIh/Q=="}, "node_modules/@editorjs/marker": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@editorjs/marker/-/marker-1.4.0.tgz", "integrity": "sha512-5ipEXfL44jTTRzgNp/p/YjMO7jT08S5z4V8qA3FFJTfdhKgQyM3mvP1zpdYKw47ZZpVWMCncvk5Nto3BxihEtg==", "dependencies": {"@codexteam/icons": "^0.0.5"}}, "node_modules/@editorjs/quote": {"version": "2.7.2", "resolved": "https://registry.npmjs.org/@editorjs/quote/-/quote-2.7.2.tgz", "integrity": "sha512-C2yB6TdBJsmfcwe2rirSjPMCQ3jTz8oOwOPbOFeLDcvUgxECEIws8kZOkuxXePdWua68QwIpUFH7yWf338lwUQ==", "dependencies": {"@codexteam/icons": "^0.3.2", "@editorjs/dom": "^0.0.5"}}, "node_modules/@editorjs/quote/node_modules/@codexteam/icons": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.3.2.tgz", "integrity": "sha512-P1ep2fHoy0tv4wx85eic+uee5plDnZQ1Qa6gDfv7eHPkCXorMtVqJhzMb75o1izogh6G7380PqmFDXV3bW3Pig=="}, "node_modules/@editorjs/simple-image": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@editorjs/simple-image/-/simple-image-1.6.0.tgz", "integrity": "sha512-WvdGfQPlozwZd3PXQrJnRXk6gEYbv1U2vRupYJ6lTd3/UsLInXYUX5jSFcnGB5ZMH3bd0JDZfcb4d4Sv1/1big==", "dependencies": {"@codexteam/icons": "^0.0.6"}}, "node_modules/@editorjs/simple-image/node_modules/@codexteam/icons": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.6.tgz", "integrity": "sha512-L7Q5PET8PjKcBT5wp7VR+FCjwCi5PUp7rd/XjsgQ0CI5FJz0DphyHGRILMuDUdCW2MQT9NHbVr4QP31vwAkS/A=="}, "node_modules/@editorjs/table": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/@editorjs/table/-/table-2.4.1.tgz", "integrity": "sha512-2OtwS4+i9xYE0UCZV4NuQ4KM3dCnv6iqKEjDU5bu4Bolf7AkYTIfQ7+axJLCrb7glz3ulWXILweBpCnqYqr+AA==", "dependencies": {"@codexteam/icons": "^0.0.6"}}, "node_modules/@editorjs/table/node_modules/@codexteam/icons": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.6.tgz", "integrity": "sha512-L7Q5PET8PjKcBT5wp7VR+FCjwCi5PUp7rd/XjsgQ0CI5FJz0DphyHGRILMuDUdCW2MQT9NHbVr4QP31vwAkS/A=="}, "node_modules/@editorjs/warning": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@editorjs/warning/-/warning-1.4.0.tgz", "integrity": "sha512-JMYm3hrmN4/S6aHBHzKsJab9+8AJRPaPh9wDGE6QrVF3dj2fCo/DZcc0MaT5TN4YI5Hu4GKBH52wOKFGOyAuoA==", "dependencies": {"@codexteam/icons": "^0.0.4"}}, "node_modules/@editorjs/warning/node_modules/@codexteam/icons": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/@codexteam/icons/-/icons-0.0.4.tgz", "integrity": "sha512-V8N/TY2TGyas4wLrPIFq7bcow68b3gu8DfDt1+rrHPtXxcexadKauRJL6eQgfG7Z0LCrN4boLRawR4S9gjIh/Q=="}, "node_modules/@floating-ui/core": {"version": "1.6.2", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.0"}}, "node_modules/@floating-ui/dom": {"version": "1.6.5", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.0.0", "@floating-ui/utils": "^0.2.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.2", "license": "MIT"}, "node_modules/@kurkle/color": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.2.tgz", "integrity": "sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw=="}, "node_modules/@orchidjs/sifter": {"version": "1.0.3", "license": "Apache-2.0", "dependencies": {"@orchidjs/unicode-variants": "^1.0.4"}}, "node_modules/@orchidjs/unicode-variants": {"version": "1.0.4", "license": "Apache-2.0"}, "node_modules/@popperjs/core": {"version": "2.11.8", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@vue/reactivity": {"version": "3.1.5", "license": "MIT", "dependencies": {"@vue/shared": "3.1.5"}}, "node_modules/@vue/shared": {"version": "3.1.5", "license": "MIT"}, "node_modules/alpinejs": {"version": "3.13.10", "license": "MIT", "dependencies": {"@vue/reactivity": "~3.1.1"}}, "node_modules/chart.js": {"version": "4.4.3", "resolved": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.3.tgz", "integrity": "sha512-qK1gkGSRYcJzqrrzdR6a+I0vQ4/R+SoODXyAjscQ/4mzuNzySaMCd+hyVxitSY1+L2fjPD1Gbn+ibNqRmwQeLw==", "dependencies": {"@kurkle/color": "^0.3.0"}, "engines": {"pnpm": ">=8"}}, "node_modules/chartjs-adapter-date-fns": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/chartjs-adapter-date-fns/-/chartjs-adapter-date-fns-3.0.0.tgz", "integrity": "sha512-Rs3iEB3Q5pJ973J93OBTpnP7qoGwvq3nUnoMdtxO+9aoJof7UFcRbWcIDteXuYd1fgAvct/32T9qaLyLuZVwCg==", "peerDependencies": {"chart.js": ">=2.8.0", "date-fns": ">=2.0.0"}}, "node_modules/chartjs-plugin-autocolors": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/chartjs-plugin-autocolors/-/chartjs-plugin-autocolors-0.2.2.tgz", "integrity": "sha512-zKSivIHRL3yB6a47bcxOfIEOEByL+AMXTtcnscb7I3Vu4PGNCyPe4C+4XxLGvR52lMYgFYcMcYmaXksi1bbA+A==", "peerDependencies": {"@kurkle/color": "^0.3.1", "chart.js": ">=2"}}, "node_modules/date-fns": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-3.6.0.tgz", "integrity": "sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==", "peer": true, "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}, "node_modules/esbuild": {"version": "0.12.29", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}}, "node_modules/luxon": {"version": "3.4.4", "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/phoenix": {"resolved": "../deps/phoenix", "link": true}, "node_modules/phoenix_html": {"resolved": "../deps/phoenix_html", "link": true}, "node_modules/phoenix_live_view": {"resolved": "../deps/phoenix_live_view", "link": true}, "node_modules/tippy.js": {"version": "6.3.7", "license": "MIT", "dependencies": {"@popperjs/core": "^2.9.0"}}, "node_modules/tom-select": {"version": "2.3.1", "license": "Apache-2.0", "dependencies": {"@orchidjs/sifter": "^1.0.3", "@orchidjs/unicode-variants": "^1.0.4"}, "engines": {"node": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/tom-select"}}}}