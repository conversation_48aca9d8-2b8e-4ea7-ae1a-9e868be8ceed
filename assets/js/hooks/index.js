import ExampleHook from "./example-hook";
import Resize<PERSON><PERSON><PERSON><PERSON>Hook from "./resize-textarea-hook";
import <PERSON><PERSON><PERSON>Hook from "./clear-flash-hook";
import ScrollTopHook from "./scroll-top-hook";
import ColorSchemeHook from "./color-scheme-hook";
import <PERSON>lipboardHook from "./clipboard-hook";
import <PERSON>ip<PERSON><PERSON>ook from "./tippy-hook";
import LocalTimeHook from "./local-time-hook";
import ComboBoxHook from "./combo-box-hook";
import FloatingHook from "./floating-hook";
import AutosaveIndicator from "./autosave-indicator-hook";
import NotificationBellHook from "./notification-bell-hook";
import ChartJsHook from "./chart-js-hook";
import FocusBySelectorHook from "./focus-by-selector-hook";
import MicrophoneHook from "./microphone-hook";
import EditorJsHook from "./editorjs-hook";
import AuroraHook from "./aurora-hook";

// Either add hooks here or create a new file for each like ExampleHook

export default {
  Aurora<PERSON>ook,
  <PERSON>ample<PERSON>ook,
  <PERSON>size<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ColorSchemeHook,
  <PERSON>lipboard<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  LocalTime<PERSON>ook,
  ComboBoxHook,
  FloatingHook,
  AutosaveIndicator,
  NotificationBellHook,
  ChartJsHook,
  FocusBySelectorHook,
  MicrophoneHook,
  EditorJsHook,
};
