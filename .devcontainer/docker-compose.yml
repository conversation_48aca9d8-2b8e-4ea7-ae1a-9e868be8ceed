version: "3.8"

services:
  elixir:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Elixir Version: 1.9, 1.10, 1.10.4, ...
        VARIANT: "1.17.2"
        # Phoenix Version: 1.4.17, 1.5.4, ...
        PHOENIX_VERSION: "1.7.17"
        # Node Version: 12, 14, ...
        NODE_VERSION: "20"

    volumes:
      - ..:/workspace:cached
    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

  db:
    image: postgres:15.7
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: app

volumes:
  postgres-data:
