{
	"name": "Elixir, Phoenix, Node.js & PostgresSQL (Community)",
	"dockerComposeFile": "docker-compose.yml",
	"service": "elixir",
	"workspaceFolder": "/workspace",

	// Configure tool-specific properties.
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"jakebecker.elixir-ls",
				"ms-azuretools.vscode-docker",
				"phoenixframework.phoenix"
			]
		}
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// This can be used to network with other containers or with the host. 
	"forwardPorts": [4000, 4001, 5432],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "mix deps.get"

	// Uncomment to connect as a non-root user. See https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode",
	"features": {
		"ghcr.io/jungaretti/features/make:1": {}
	}
}
