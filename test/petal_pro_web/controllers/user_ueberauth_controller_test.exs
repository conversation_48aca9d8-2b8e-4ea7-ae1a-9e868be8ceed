defmodule PetalProWeb.UserUeberauthControllerTest do
  use PetalProWeb.ConnCase, async: true

  alias PetalPro.Accounts.User
  alias PetalPro.Repo

  test "creates user from Google information", %{conn: conn} do
    user_info = %{
      credentials: %{token: "token"},
      info: %{email: "<EMAIL>", first_name: "<PERSON>", last_name: "<PERSON>", image: "image.jpg"},
      provider: :google
    }

    conn =
      conn
      |> assign(:ueberauth_auth, user_info)
      |> get("/auth/google/callback")

    latest_user = Repo.last(User)
    assert latest_user.name == "#{user_info.info.first_name} #{user_info.info.last_name}"
    assert latest_user.avatar == user_info.info.image
    assert latest_user.email == user_info.info.email
    assert redirected_to(conn) == PetalProWeb.Helpers.home_path(latest_user)
  end

  test "creates user from Github information", %{conn: conn} do
    user_info = %{
      credentials: %{token: "token"},
      info: %{email: "<EMAIL>", name: "<PERSON>", image: "image.jpg"},
      provider: :github
    }

    conn =
      conn
      |> assign(:ueberauth_auth, user_info)
      |> get("/auth/github/callback")

    latest_user = Repo.last(User)
    assert latest_user.name == user_info.info.name
    assert latest_user.avatar == user_info.info.image
    assert latest_user.email == user_info.info.email
    assert redirected_to(conn) == PetalProWeb.Helpers.home_path(latest_user)
  end
end
