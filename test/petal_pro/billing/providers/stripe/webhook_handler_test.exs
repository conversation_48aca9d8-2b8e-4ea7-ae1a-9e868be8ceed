defmodule PetalPro.Billing.Providers.Stripe.WebhookHandlerTest do
  use PetalPro.DataCase

  import PetalPro.AccountsFixtures
  import PetalPro.BillingFixtures
  import PetalPro.OrgsFixtures

  alias PetalPro.Billing.Customers
  alias PetalPro.Billing.Plans
  alias PetalPro.Billing.Providers.Stripe.Provider
  alias PetalPro.Billing.Providers.Stripe.WebhookHandler
  alias PetalPro.Repo

  describe "handle_event/1 for user as a source" do
    setup do
      user = confirmed_user_fixture()

      stripe_event =
        customer_subscription_updated_event(
          metadata: %{
            source: :user,
            source_id: user.id
          }
        )

      stripe_subscription = stripe_event.data.object
      stripe_subscription_id = stripe_subscription.id

      customer =
        billing_customer_fixture(%{
          user_id: user.id,
          provider_customer_id: stripe_subscription.customer
        })

      expect(Provider, :retrieve_subscription, fn ^stripe_subscription_id ->
        {:ok, stripe_subscription}
      end)

      [customer: customer, stripe_event: stripe_event]
    end

    test "creates billing subscription", %{customer: customer, stripe_event: stripe_event} do
      assert %{subscriptions: []} = Customers.get_customer_by(email: customer.email)

      Oban.Testing.with_testing_mode(:inline, fn ->
        assert {:ok, %Oban.Job{}} = WebhookHandler.handle_event(stripe_event)
      end)

      assert %{subscriptions: [subscription]} = Customers.get_customer_by(email: customer.email)

      assert subscription.plan_id == "plan1-1"
      assert subscription.status == "active"
      assert subscription.provider_subscription_id == stripe_event.data.object.id
    end

    test "creates a log called billing.create_subscription", %{stripe_event: stripe_event} do
      Oban.Testing.with_testing_mode(:inline, fn ->
        assert {:ok, %Oban.Job{}} = WebhookHandler.handle_event(stripe_event)
      end)

      log = Repo.last(PetalPro.Logs.Log)
      assert log.action == "billing.create_subscription"
    end
  end

  describe "handle_event/1 for organization as a source" do
    setup do
      org = org_fixture()

      stripe_event =
        customer_subscription_updated_event(
          metadata: %{
            source: :org,
            source_id: org.id
          }
        )

      stripe_subscription = stripe_event.data.object
      stripe_subscription_id = stripe_subscription.id

      customer =
        billing_customer_fixture(%{
          org_id: org.id,
          provider_customer_id: stripe_subscription.customer
        })

      expect(Provider, :retrieve_subscription, fn ^stripe_subscription_id ->
        {:ok, stripe_subscription}
      end)

      [customer: customer, stripe_event: stripe_event]
    end

    test "creates billing subscription", %{customer: customer, stripe_event: stripe_event} do
      assert %{subscriptions: []} = Customers.get_customer_by(email: customer.email)

      Oban.Testing.with_testing_mode(:inline, fn ->
        assert {:ok, %Oban.Job{}} = WebhookHandler.handle_event(stripe_event)
      end)

      assert %{subscriptions: [subscription]} = Customers.get_customer_by(email: customer.email)

      assert subscription.plan_id == "plan1-1"
      assert subscription.status == "active"
      assert subscription.provider_subscription_id == stripe_event.data.object.id
    end

    test "creates a log called billing.subscribe_subscription", %{stripe_event: stripe_event} do
      Oban.Testing.with_testing_mode(:inline, fn ->
        assert {:ok, %Oban.Job{}} = WebhookHandler.handle_event(stripe_event)
      end)

      log = Repo.last(PetalPro.Logs.Log)
      assert log.action == "billing.create_subscription"
    end
  end

  defp customer_subscription_updated_event(opts) do
    event = stripe_event()

    %Stripe.Event{
      event
      | data: %{
          event.data
          | object: %{
              event.data.object
              | metadata: opts[:metadata],
                items: %{
                  event.data.object.items
                  | data: [
                      %{
                        List.first(event.data.object.items.data)
                        | price: %{
                            List.first(event.data.object.items.data).price
                            | # this is necessary so that a test plan is found
                              # by searching for the plan ID
                              id:
                                Plans.get_plan_by_id!("plan1-1").items
                                |> List.first()
                                |> Map.get(:price)
                          }
                      }
                    ]
                }
            }
        }
    }
  end

  # `%Stripe.Event{}` is generated by Stripe CLI and captured in the webhook,
  #  do not change anything here for simple copy/pasting when upgrading.
  #  Change `customer_subscription_updated_event/1` instead.
  #
  # Stripe CLI: `stripe trigger customer.subscription.updated`
  #
  # See https://stripe.com/docs/cli/trigger
  defp stripe_event do
    %Stripe.Event{
      account: nil,
      api_version: "2022-11-15",
      created: 1_703_398_252,
      data: %{
        object: %Stripe.Subscription{
          default_tax_rates: [],
          pending_update: nil,
          id: "sub_1OQkthIWVkWpNCp7tGAeVHm7",
          transfer_data: nil,
          items: %Stripe.List{
            object: "list",
            data: [
              %Stripe.SubscriptionItem{
                billing_thresholds: nil,
                created: 1_703_398_250,
                id: "si_PFFOthe2UVSHtN",
                metadata: %{},
                object: "subscription_item",
                plan: %Stripe.Plan{
                  active: true,
                  aggregate_usage: nil,
                  amount: 1500,
                  amount_decimal: "1500",
                  billing_scheme: "per_unit",
                  created: 1_703_398_249,
                  currency: "usd",
                  id: "price_1OQkthIWVkWpNCp7UtP4pKQe",
                  interval: "month",
                  interval_count: 1,
                  livemode: false,
                  metadata: %{},
                  nickname: nil,
                  object: "plan",
                  product: "prod_PFFO8n5iqbIaf7",
                  tiers: nil,
                  tiers_mode: nil,
                  transform_usage: nil,
                  trial_period_days: nil,
                  usage_type: "licensed"
                },
                price: %Stripe.Price{
                  active: true,
                  billing_scheme: "per_unit",
                  created: 1_703_398_249,
                  currency: "usd",
                  currency_options: nil,
                  custom_unit_amount: nil,
                  id: "price_1OQkthIWVkWpNCp7UtP4pKQe",
                  livemode: false,
                  lookup_key: nil,
                  metadata: %{},
                  nickname: nil,
                  object: "price",
                  product: "prod_PFFO8n5iqbIaf7",
                  recurring: %{
                    interval: "month",
                    interval_count: 1,
                    trial_period_days: nil,
                    aggregate_usage: nil,
                    usage_type: "licensed"
                  },
                  tax_behavior: "unspecified",
                  tiers: nil,
                  tiers_mode: nil,
                  transform_quantity: nil,
                  type: "recurring",
                  unit_amount: 1500,
                  unit_amount_decimal: "1500"
                },
                quantity: 1,
                subscription: "sub_1OQkthIWVkWpNCp7tGAeVHm7",
                tax_rates: []
              }
            ],
            has_more: false,
            total_count: 1,
            url: "/v1/subscription_items?subscription=sub_1OQkthIWVkWpNCp7tGAeVHm7"
          },
          status: "active",
          current_period_start: 1_703_398_249,
          trial_start: nil,
          trial_settings: %{
            end_behavior: %{missing_payment_method: "create_invoice"}
          },
          cancel_at: nil,
          trial_end: nil,
          created: 1_703_398_249,
          currency: "usd",
          latest_invoice: "in_1OQkthIWVkWpNCp7wyoaDuFG",
          current_period_end: 1_706_076_649,
          canceled_at: nil,
          schedule: nil,
          automatic_tax: %{enabled: false},
          test_clock: nil,
          billing_cycle_anchor: 1_703_398_249,
          object: "subscription",
          cancel_at_period_end: false,
          default_source: nil,
          next_pending_invoice_item_invoice: nil,
          billing_thresholds: nil,
          pending_invoice_item_interval: nil,
          application: nil,
          payment_settings: %{
            payment_method_types: nil,
            payment_method_options: nil,
            save_default_payment_method: "off"
          },
          days_until_due: nil,
          ended_at: nil,
          customer: "cus_PFFOfROcDPrYZH",
          pause_collection: nil,
          on_behalf_of: nil,
          start_date: 1_703_398_249,
          discount: nil,
          pending_setup_intent: nil,
          cancellation_details: %{reason: nil, comment: nil, feedback: nil},
          description: nil,
          metadata: %{"foo" => "bar"},
          default_payment_method: nil,
          collection_method: "charge_automatically",
          livemode: false,
          application_fee_percent: nil
        },
        previous_attributes: %{metadata: %{foo: nil}}
      },
      id: "evt_1OQktlIWVkWpNCp7YvAFzcAa",
      livemode: false,
      object: "event",
      pending_webhooks: 3,
      request: %{
        id: "req_ToSthAMnu46wXV",
        idempotency_key: "57c8b378-6c47-4458-b449-5ce542d6d1b1"
      },
      type: "customer.subscription.updated"
    }
  end
end
