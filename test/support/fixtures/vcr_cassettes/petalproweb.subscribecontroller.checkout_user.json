[{"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "email=user-576460752303399774%40example.com", "url": "https://api.stripe.com/v1/customers", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Idempotency-Key": "30tgu75l2oafmp6vag000n73", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "post", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"cus_SDz3JYkFPRYVnp\",\n  \"object\": \"customer\",\n  \"address\": null,\n  \"balance\": 0,\n  \"created\": 1746005896,\n  \"currency\": null,\n  \"default_source\": null,\n  \"delinquent\": false,\n  \"description\": null,\n  \"discount\": null,\n  \"email\": \"<EMAIL>\",\n  \"invoice_prefix\": \"U9NNLZLR\",\n  \"invoice_settings\": {\n    \"custom_fields\": null,\n    \"default_payment_method\": null,\n    \"footer\": null,\n    \"rendering_options\": null\n  },\n  \"livemode\": false,\n  \"metadata\": {},\n  \"name\": null,\n  \"next_invoice_sequence\": 1,\n  \"phone\": null,\n  \"preferred_locales\": [],\n  \"shipping\": null,\n  \"tax_exempt\": \"none\",\n  \"test_clock\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:38:16 GMT", "Content-Type": "application/json", "Content-Length": "647", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=fRP2ZV-GN5sf9IEMPSQACSRVwzfLExKsgLgs8xamhnod0Kf0rNigxZnQclpJGK-AeSmYaM0-AsJJzQTi", "Idempotency-Key": "30tgu75l2oafmp6vag000n73", "Original-Request": "req_1Zg32kZBVYOtQn", "Request-Id": "req_1Zg32kZBVYOtQn", "Stripe-Should-Retry": "false", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "mode=subscription&allow_promotion_codes=true&customer=cus_SDz3JYkFPRYVnp&success_url=http%3A%2F%2Flocalhost%3A4002%2Fapp%2Fsubscribe%2Fsuccess%3Fcustomer_id%3D579&cancel_url=http%3A%2F%2Flocalhost%3A4002%2Fapp%2Fsubscribe&line_items%5B0%5D%5Bprice%5D=price_1OQj8TIWVkWpNCp7ZlUSOaI9&line_items%5B0%5D%5Bquantity%5D=1&client_reference_id=579&subscription_data%5Bmetadata%5D%5Bsource%5D=user&subscription_data%5Bmetadata%5D%5Bsource_id%5D=4603&subscription_data%5Btrial_period_days%5D=7", "url": "https://api.stripe.com/v1/checkout/sessions", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Idempotency-Key": "30tgu76bj6rbup6vag000n83", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "post", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"cs_test_b16VLOvCSxhHM8KtsTsjVQ43Jux1SyzV0cnrH9rtV9KlM0vunhnAGwz9V1\",\n  \"object\": \"checkout.session\",\n  \"adaptive_pricing\": null,\n  \"after_expiration\": null,\n  \"allow_promotion_codes\": true,\n  \"amount_subtotal\": 0,\n  \"amount_total\": 0,\n  \"automatic_tax\": {\n    \"enabled\": false,\n    \"liability\": null,\n    \"provider\": null,\n    \"status\": null\n  },\n  \"billing_address_collection\": null,\n  \"cancel_url\": \"http://localhost:4002/app/subscribe\",\n  \"client_reference_id\": \"579\",\n  \"client_secret\": null,\n  \"collected_information\": {\n    \"shipping_details\": null\n  },\n  \"consent\": null,\n  \"consent_collection\": null,\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"currency_conversion\": null,\n  \"custom_fields\": [],\n  \"custom_text\": {\n    \"after_submit\": null,\n    \"shipping_address\": null,\n    \"submit\": null,\n    \"terms_of_service_acceptance\": null\n  },\n  \"customer\": \"cus_SDz3JYkFPRYVnp\",\n  \"customer_creation\": null,\n  \"customer_details\": {\n    \"address\": null,\n    \"email\": \"<EMAIL>\",\n    \"name\": null,\n    \"phone\": null,\n    \"tax_exempt\": \"none\",\n    \"tax_ids\": null\n  },\n  \"customer_email\": null,\n  \"discounts\": [],\n  \"expires_at\": **********,\n  \"invoice\": null,\n  \"invoice_creation\": null,\n  \"livemode\": false,\n  \"locale\": null,\n  \"metadata\": {},\n  \"mode\": \"subscription\",\n  \"payment_intent\": null,\n  \"payment_link\": null,\n  \"payment_method_collection\": \"always\",\n  \"payment_method_configuration_details\": {\n    \"id\": \"pmc_1KQkIYIWVkWpNCp7P1tL9RwN\",\n    \"parent\": null\n  },\n  \"payment_method_options\": {\n    \"card\": {\n      \"request_three_d_secure\": \"automatic\"\n    }\n  },\n  \"payment_method_types\": [\n    \"card\",\n    \"link\"\n  ],\n  \"payment_status\": \"unpaid\",\n  \"permissions\": null,\n  \"phone_number_collection\": {\n    \"enabled\": false\n  },\n  \"recovered_from\": null,\n  \"saved_payment_method_options\": {\n    \"allow_redisplay_filters\": [\n      \"always\"\n    ],\n    \"payment_method_remove\": null,\n    \"payment_method_save\": null\n  },\n  \"setup_intent\": null,\n  \"shipping_address_collection\": null,\n  \"shipping_cost\": null,\n  \"shipping_details\": null,\n  \"shipping_options\": [],\n  \"status\": \"open\",\n  \"submit_type\": null,\n  \"subscription\": null,\n  \"success_url\": \"http://localhost:4002/app/subscribe/success?customer_id=579\",\n  \"total_details\": {\n    \"amount_discount\": 0,\n    \"amount_shipping\": 0,\n    \"amount_tax\": 0\n  },\n  \"ui_mode\": \"hosted\",\n  \"url\": \"https://checkout.stripe.com/c/pay/cs_test_b16VLOvCSxhHM8KtsTsjVQ43Jux1SyzV0cnrH9rtV9KlM0vunhnAGwz9V1#fid2cGd2ZndsdXFsamtQa2x0cGBrYHZ2QGtkZ2lgYSc%2FY2RpdmApJ2R1bE5gfCc%2FJ3VuWnFgdnFaMDROVEt8UExSU25SdUtGdTJSPDJJZ2FTdTdHZ0IyS2BIMGQ3XDRgPE1cSH1HbVFkSj1oXU5OQ01zUEN9V3VvRlM8ZzZ1fVVcQ1ZSTH9LYDNxN1FcVmsxQDM1NUdkYTEwYDM2JyknY3dqaFZgd3Ngdyc%2FcXdwYCknaWR8anBxUXx1YCc%2FJ2hwaXFsWmxxYGgnKSdga2RnaWBVaWRmYG1qaWFgd3YnP3F3cGB4JSUl\",\n  \"wallet_options\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:38:17 GMT", "Content-Type": "application/json", "Content-Length": "2834", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=fRP2ZV-GN5sf9IEMPSQACSRVwzfLExKsgLgs8xamhnod0Kf0rNigxZnQclpJGK-AeSmYaM0-AsJJzQTi", "Idempotency-Key": "30tgu76bj6rbup6vag000n83", "Original-Request": "req_rACgkAvTpUbZkN", "Request-Id": "req_rACgkAvTpUbZkN", "Stripe-Should-Retry": "false", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}]