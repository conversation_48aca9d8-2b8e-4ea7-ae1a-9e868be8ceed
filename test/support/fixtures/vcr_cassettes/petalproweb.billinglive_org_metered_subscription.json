[{"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/subscriptions/sub_1RWB09IWVkWpNCp7q9RhQRt2", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n  \"object\": \"subscription\",\n  \"application\": null,\n  \"application_fee_percent\": null,\n  \"automatic_tax\": {\n    \"disabled_reason\": null,\n    \"enabled\": true,\n    \"liability\": {\n      \"type\": \"self\"\n    }\n  },\n  \"billing_cycle_anchor\": **********,\n  \"billing_cycle_anchor_config\": null,\n  \"billing_thresholds\": null,\n  \"cancel_at\": null,\n  \"cancel_at_period_end\": false,\n  \"canceled_at\": null,\n  \"cancellation_details\": {\n    \"comment\": null,\n    \"feedback\": null,\n    \"reason\": null\n  },\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"current_period_end\": **********,\n  \"current_period_start\": **********,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"days_until_due\": null,\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"discounts\": [],\n  \"ended_at\": null,\n  \"invoice_settings\": {\n    \"account_tax_ids\": null,\n    \"issuer\": {\n      \"type\": \"self\"\n    }\n  },\n  \"items\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"si_SR36dgFQ0BkALS\",\n        \"object\": \"subscription_item\",\n        \"billing_thresholds\": null,\n        \"created\": **********,\n        \"current_period_end\": **********,\n        \"current_period_start\": **********,\n        \"discounts\": [],\n        \"metadata\": {},\n        \"plan\": {\n          \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 4,\n          \"amount_decimal\": \"4\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n          \"nickname\": null,\n          \"product\": \"prod_S8bnyI5qmG5mEz\",\n          \"tiers_mode\": null,\n          \"transform_usage\": {\n            \"divide_by\": 100,\n            \"round\": \"up\"\n          },\n          \"trial_period_days\": null,\n          \"usage_type\": \"metered\"\n        },\n        \"price\": {\n          \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_S8bnyI5qmG5mEz\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n            \"trial_period_days\": null,\n            \"usage_type\": \"metered\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": {\n            \"divide_by\": 100,\n            \"round\": \"up\"\n          },\n          \"type\": \"recurring\",\n          \"unit_amount\": 4,\n          \"unit_amount_decimal\": \"4\"\n        },\n        \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n        \"tax_rates\": []\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/subscription_items?subscription=sub_1RWB09IWVkWpNCp7q9RhQRt2\"\n  },\n  \"latest_invoice\": \"in_1RWB09IWVkWpNCp7DTky7Q3E\",\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_pending_invoice_item_invoice\": null,\n  \"on_behalf_of\": null,\n  \"pause_collection\": null,\n  \"payment_settings\": {\n    \"payment_method_options\": null,\n    \"payment_method_types\": null,\n    \"save_default_payment_method\": \"off\"\n  },\n  \"pending_invoice_item_interval\": null,\n  \"pending_setup_intent\": null,\n  \"pending_update\": null,\n  \"plan\": {\n    \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n    \"object\": \"plan\",\n    \"active\": true,\n    \"aggregate_usage\": null,\n    \"amount\": 4,\n    \"amount_decimal\": \"4\",\n    \"billing_scheme\": \"per_unit\",\n    \"created\": **********,\n    \"currency\": \"aud\",\n    \"interval\": \"month\",\n    \"interval_count\": 1,\n    \"livemode\": false,\n    \"metadata\": {},\n    \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n    \"nickname\": null,\n    \"product\": \"prod_S8bnyI5qmG5mEz\",\n    \"tiers_mode\": null,\n    \"transform_usage\": {\n      \"divide_by\": 100,\n      \"round\": \"up\"\n    },\n    \"trial_period_days\": null,\n    \"usage_type\": \"metered\"\n  },\n  \"quantity\": 1,\n  \"schedule\": null,\n  \"start_date\": **********,\n  \"status\": \"active\",\n  \"test_clock\": null,\n  \"transfer_data\": null,\n  \"trial_end\": null,\n  \"trial_settings\": {\n    \"end_behavior\": {\n      \"missing_payment_method\": \"create_invoice\"\n    }\n  },\n  \"trial_start\": null\n}", "headers": {"Server": "nginx", "Date": "Thu, 05 Jun 2025 06:35:22 GMT", "Content-Type": "application/json", "Content-Length": "4684", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=g2eZfiz_eC1F8DaioSFyixjK8sNgPCJcF-FGigp6WKbZUyYLKNF925PZbPH5Cbb_YVRy8rTOI8iYPZdj", "Request-Id": "req_tZ6fWKaccU9jGj", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/products/prod_S8bnyI5qmG5mEz", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"prod_S8bnyI5qmG5mEz\",\n  \"object\": \"product\",\n  \"active\": true,\n  \"attributes\": [],\n  \"created\": **********,\n  \"default_price\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n  \"description\": \"Just to test usage-based billing\",\n  \"features\": [],\n  \"images\": [],\n  \"livemode\": false,\n  \"marketing_features\": [],\n  \"metadata\": {},\n  \"name\": \"Admin AI\",\n  \"package_dimensions\": null,\n  \"shippable\": null,\n  \"statement_descriptor\": null,\n  \"tax_code\": \"txcd_10202000\",\n  \"type\": \"service\",\n  \"unit_label\": null,\n  \"updated\": 1744766154,\n  \"url\": null\n}", "headers": {"Server": "nginx", "Date": "Thu, 05 Jun 2025 06:35:22 GMT", "Content-Type": "application/json", "Content-Length": "545", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=g2eZfiz_eC1F8DaioSFyixjK8sNgPCJcF-FGigp6WKbZUyYLKNF925PZbPH5Cbb_YVRy8rTOI8iYPZdj", "Request-Id": "req_9x0Aos3obZyV84", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/invoices?limit=2&subscription=sub_1RWB09IWVkWpNCp7q9RhQRt2", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"object\": \"list\",\n  \"data\": [\n    {\n      \"id\": \"in_1RWB09IWVkWpNCp7DTky7Q3E\",\n      \"object\": \"invoice\",\n      \"account_country\": \"AU\",\n      \"account_name\": \"Everfree Pty Ltd\",\n      \"account_tax_ids\": null,\n      \"amount_due\": 0,\n      \"amount_overpaid\": 0,\n      \"amount_paid\": 0,\n      \"amount_remaining\": 0,\n      \"amount_shipping\": 0,\n      \"application\": null,\n      \"application_fee_amount\": null,\n      \"attempt_count\": 0,\n      \"attempted\": true,\n      \"auto_advance\": false,\n      \"automatic_tax\": {\n        \"disabled_reason\": null,\n        \"enabled\": true,\n        \"liability\": {\n          \"type\": \"self\"\n        },\n        \"provider\": \"stripe\",\n        \"status\": \"complete\"\n      },\n      \"automatically_finalizes_at\": null,\n      \"billing_reason\": \"subscription_create\",\n      \"charge\": null,\n      \"collection_method\": \"charge_automatically\",\n      \"created\": **********,\n      \"currency\": \"aud\",\n      \"custom_fields\": null,\n      \"customer\": \"cus_PFDXVnCJHqastp\",\n      \"customer_address\": {\n        \"city\": \"\",\n        \"country\": \"AU\",\n        \"line1\": \"\",\n        \"line2\": \"\",\n        \"postal_code\": \"\",\n        \"state\": \"\"\n      },\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_name\": \"<DO NOT DELETE> Petal Pro Test User\",\n      \"customer_phone\": null,\n      \"customer_shipping\": {\n        \"address\": {\n          \"city\": \"\",\n          \"country\": \"AU\",\n          \"line1\": \"\",\n          \"line2\": \"\",\n          \"postal_code\": \"\",\n          \"state\": \"\"\n        },\n        \"name\": \"<DO NOT DELETE> Petal Pro Test User\",\n        \"phone\": \"\"\n      },\n      \"customer_tax_exempt\": \"none\",\n      \"customer_tax_ids\": [],\n      \"default_payment_method\": null,\n      \"default_source\": null,\n      \"default_tax_rates\": [],\n      \"description\": null,\n      \"discount\": null,\n      \"discounts\": [],\n      \"due_date\": null,\n      \"effective_at\": **********,\n      \"ending_balance\": 0,\n      \"footer\": null,\n      \"from_invoice\": null,\n      \"hosted_invoice_url\": \"https://invoice.stripe.com/i/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TUjM2ZHJkejB0SnlUU1V1SjdZU3BqNHFGWW5sRUhxLDEzOTY0NjEyMw02003RCTlyOr?s=ap\",\n      \"invoice_pdf\": \"https://pay.stripe.com/invoice/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TUjM2ZHJkejB0SnlUU1V1SjdZU3BqNHFGWW5sRUhxLDEzOTY0NjEyMw02003RCTlyOr/pdf?s=ap\",\n      \"issuer\": {\n        \"type\": \"self\"\n      },\n      \"last_finalization_error\": null,\n      \"latest_revision\": null,\n      \"lines\": {\n        \"object\": \"list\",\n        \"data\": [\n          {\n            \"id\": \"il_1RWB09IWVkWpNCp7N3sRpBmj\",\n            \"object\": \"line_item\",\n            \"amount\": 0,\n            \"amount_excluding_tax\": 0,\n            \"currency\": \"aud\",\n            \"description\": \"0 × Admin AI (at $0.04 per 100 units / month)\",\n            \"discount_amounts\": [],\n            \"discountable\": true,\n            \"discounts\": [],\n            \"invoice\": \"in_1RWB09IWVkWpNCp7DTky7Q3E\",\n            \"livemode\": false,\n            \"metadata\": {},\n            \"parent\": {\n              \"invoice_item_details\": null,\n              \"subscription_item_details\": {\n                \"invoice_item\": null,\n                \"proration\": false,\n                \"proration_details\": {\n                  \"credited_items\": null\n                },\n                \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n                \"subscription_item\": \"si_SR36dgFQ0BkALS\"\n              },\n              \"type\": \"subscription_item_details\"\n            },\n            \"period\": {\n              \"end\": 1751611200,\n              \"start\": **********\n            },\n            \"plan\": {\n              \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n              \"object\": \"plan\",\n              \"active\": true,\n              \"aggregate_usage\": null,\n              \"amount\": 4,\n              \"amount_decimal\": \"4\",\n              \"billing_scheme\": \"per_unit\",\n              \"created\": **********,\n              \"currency\": \"aud\",\n              \"interval\": \"month\",\n              \"interval_count\": 1,\n              \"livemode\": false,\n              \"metadata\": {},\n              \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n              \"nickname\": null,\n              \"product\": \"prod_S8bnyI5qmG5mEz\",\n              \"tiers_mode\": null,\n              \"transform_usage\": {\n                \"divide_by\": 100,\n                \"round\": \"up\"\n              },\n              \"trial_period_days\": null,\n              \"usage_type\": \"metered\"\n            },\n            \"pretax_credit_amounts\": [],\n            \"price\": {\n              \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n              \"object\": \"price\",\n              \"active\": true,\n              \"billing_scheme\": \"per_unit\",\n              \"created\": **********,\n              \"currency\": \"aud\",\n              \"custom_unit_amount\": null,\n              \"livemode\": false,\n              \"lookup_key\": null,\n              \"metadata\": {},\n              \"nickname\": null,\n              \"product\": \"prod_S8bnyI5qmG5mEz\",\n              \"recurring\": {\n                \"aggregate_usage\": null,\n                \"interval\": \"month\",\n                \"interval_count\": 1,\n                \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n                \"trial_period_days\": null,\n                \"usage_type\": \"metered\"\n              },\n              \"tax_behavior\": \"inclusive\",\n              \"tiers_mode\": null,\n              \"transform_quantity\": {\n                \"divide_by\": 100,\n                \"round\": \"up\"\n              },\n              \"type\": \"recurring\",\n              \"unit_amount\": 4,\n              \"unit_amount_decimal\": \"4\"\n            },\n            \"pricing\": {\n              \"price_details\": {\n                \"price\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n                \"product\": \"prod_S8bnyI5qmG5mEz\"\n              },\n              \"type\": \"price_details\",\n              \"unit_amount_decimal\": \"4\"\n            },\n            \"proration\": false,\n            \"proration_details\": {\n              \"credited_items\": null\n            },\n            \"quantity\": 0,\n            \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n            \"subscription_item\": \"si_SR36dgFQ0BkALS\",\n            \"tax_amounts\": [\n              {\n                \"amount\": 0,\n                \"inclusive\": true,\n                \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n                \"taxability_reason\": \"not_collecting\",\n                \"taxable_amount\": 0\n              }\n            ],\n            \"tax_rates\": [],\n            \"taxes\": [\n              {\n                \"amount\": 0,\n                \"tax_behavior\": \"inclusive\",\n                \"tax_rate_details\": {\n                  \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n                },\n                \"taxability_reason\": \"not_collecting\",\n                \"taxable_amount\": 0,\n                \"type\": \"tax_rate_details\"\n              }\n            ],\n            \"type\": \"subscription\",\n            \"unit_amount_excluding_tax\": null\n          }\n        ],\n        \"has_more\": false,\n        \"total_count\": 1,\n        \"url\": \"/v1/invoices/in_1RWB09IWVkWpNCp7DTky7Q3E/lines\"\n      },\n      \"livemode\": false,\n      \"metadata\": {},\n      \"next_payment_attempt\": null,\n      \"number\": \"B3B25542-0006\",\n      \"on_behalf_of\": null,\n      \"paid\": true,\n      \"paid_out_of_band\": false,\n      \"parent\": {\n        \"quote_details\": null,\n        \"subscription_details\": {\n          \"metadata\": {},\n          \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\"\n        },\n        \"type\": \"subscription_details\"\n      },\n      \"payment_intent\": null,\n      \"payment_settings\": {\n        \"default_mandate\": null,\n        \"payment_method_options\": null,\n        \"payment_method_types\": null\n      },\n      \"period_end\": **********,\n      \"period_start\": **********,\n      \"post_payment_credit_notes_amount\": 0,\n      \"pre_payment_credit_notes_amount\": 0,\n      \"quote\": null,\n      \"receipt_number\": null,\n      \"rendering\": null,\n      \"rendering_options\": null,\n      \"shipping_cost\": null,\n      \"shipping_details\": null,\n      \"starting_balance\": 0,\n      \"statement_descriptor\": null,\n      \"status\": \"paid\",\n      \"status_transitions\": {\n        \"finalized_at\": **********,\n        \"marked_uncollectible_at\": null,\n        \"paid_at\": **********,\n        \"voided_at\": null\n      },\n      \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n      \"subscription_details\": {\n        \"metadata\": {}\n      },\n      \"subtotal\": 0,\n      \"subtotal_excluding_tax\": 0,\n      \"tax\": 0,\n      \"test_clock\": null,\n      \"total\": 0,\n      \"total_discount_amounts\": [],\n      \"total_excluding_tax\": 0,\n      \"total_pretax_credit_amounts\": [],\n      \"total_tax_amounts\": [\n        {\n          \"amount\": 0,\n          \"inclusive\": true,\n          \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n          \"taxability_reason\": \"not_collecting\",\n          \"taxable_amount\": 0\n        }\n      ],\n      \"total_taxes\": [\n        {\n          \"amount\": 0,\n          \"tax_behavior\": \"inclusive\",\n          \"tax_rate_details\": {\n            \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n          },\n          \"taxability_reason\": \"not_collecting\",\n          \"taxable_amount\": 0,\n          \"type\": \"tax_rate_details\"\n        }\n      ],\n      \"transfer_data\": null,\n      \"webhooks_delivered_at\": 1749019243\n    }\n  ],\n  \"has_more\": false,\n  \"url\": \"/v1/invoices\"\n}", "headers": {"Server": "nginx", "Date": "Thu, 05 Jun 2025 06:35:23 GMT", "Content-Type": "application/json", "Content-Length": "9355", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=g2eZfiz_eC1F8DaioSFyixjK8sNgPCJcF-FGigp6WKbZUyYLKNF925PZbPH5Cbb_YVRy8rTOI8iYPZdj", "Request-Id": "req_A89rbAyU3N1C1Y", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/invoices/in_1RWB09IWVkWpNCp7DTky7Q3E", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"in_1RWB09IWVkWpNCp7DTky7Q3E\",\n  \"object\": \"invoice\",\n  \"account_country\": \"AU\",\n  \"account_name\": \"Everfree Pty Ltd\",\n  \"account_tax_ids\": null,\n  \"amount_due\": 0,\n  \"amount_overpaid\": 0,\n  \"amount_paid\": 0,\n  \"amount_remaining\": 0,\n  \"amount_shipping\": 0,\n  \"application\": null,\n  \"application_fee_amount\": null,\n  \"attempt_count\": 0,\n  \"attempted\": true,\n  \"auto_advance\": false,\n  \"automatic_tax\": {\n    \"disabled_reason\": null,\n    \"enabled\": true,\n    \"liability\": {\n      \"type\": \"self\"\n    },\n    \"provider\": \"stripe\",\n    \"status\": \"complete\"\n  },\n  \"automatically_finalizes_at\": null,\n  \"billing_reason\": \"subscription_create\",\n  \"charge\": null,\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"custom_fields\": null,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"customer_address\": {\n    \"city\": \"\",\n    \"country\": \"AU\",\n    \"line1\": \"\",\n    \"line2\": \"\",\n    \"postal_code\": \"\",\n    \"state\": \"\"\n  },\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_name\": \"<DO NOT DELETE> Petal Pro Test User\",\n  \"customer_phone\": null,\n  \"customer_shipping\": {\n    \"address\": {\n      \"city\": \"\",\n      \"country\": \"AU\",\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"postal_code\": \"\",\n      \"state\": \"\"\n    },\n    \"name\": \"<DO NOT DELETE> Petal Pro Test User\",\n    \"phone\": \"\"\n  },\n  \"customer_tax_exempt\": \"none\",\n  \"customer_tax_ids\": [],\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"discounts\": [],\n  \"due_date\": null,\n  \"effective_at\": **********,\n  \"ending_balance\": 0,\n  \"footer\": null,\n  \"from_invoice\": null,\n  \"hosted_invoice_url\": \"https://invoice.stripe.com/i/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TUjM2ZHJkejB0SnlUU1V1SjdZU3BqNHFGWW5sRUhxLDEzOTY0NjEyMw02003RCTlyOr?s=ap\",\n  \"invoice_pdf\": \"https://pay.stripe.com/invoice/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TUjM2ZHJkejB0SnlUU1V1SjdZU3BqNHFGWW5sRUhxLDEzOTY0NjEyMw02003RCTlyOr/pdf?s=ap\",\n  \"issuer\": {\n    \"type\": \"self\"\n  },\n  \"last_finalization_error\": null,\n  \"latest_revision\": null,\n  \"lines\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"il_1RWB09IWVkWpNCp7N3sRpBmj\",\n        \"object\": \"line_item\",\n        \"amount\": 0,\n        \"amount_excluding_tax\": 0,\n        \"currency\": \"aud\",\n        \"description\": \"0 × Admin AI (at $0.04 per 100 units / month)\",\n        \"discount_amounts\": [],\n        \"discountable\": true,\n        \"discounts\": [],\n        \"invoice\": \"in_1RWB09IWVkWpNCp7DTky7Q3E\",\n        \"livemode\": false,\n        \"metadata\": {},\n        \"parent\": {\n          \"invoice_item_details\": null,\n          \"subscription_item_details\": {\n            \"invoice_item\": null,\n            \"proration\": false,\n            \"proration_details\": {\n              \"credited_items\": null\n            },\n            \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n            \"subscription_item\": \"si_SR36dgFQ0BkALS\"\n          },\n          \"type\": \"subscription_item_details\"\n        },\n        \"period\": {\n          \"end\": 1751611200,\n          \"start\": **********\n        },\n        \"plan\": {\n          \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 4,\n          \"amount_decimal\": \"4\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n          \"nickname\": null,\n          \"product\": \"prod_S8bnyI5qmG5mEz\",\n          \"tiers_mode\": null,\n          \"transform_usage\": {\n            \"divide_by\": 100,\n            \"round\": \"up\"\n          },\n          \"trial_period_days\": null,\n          \"usage_type\": \"metered\"\n        },\n        \"pretax_credit_amounts\": [],\n        \"price\": {\n          \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_S8bnyI5qmG5mEz\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n            \"trial_period_days\": null,\n            \"usage_type\": \"metered\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": {\n            \"divide_by\": 100,\n            \"round\": \"up\"\n          },\n          \"type\": \"recurring\",\n          \"unit_amount\": 4,\n          \"unit_amount_decimal\": \"4\"\n        },\n        \"pricing\": {\n          \"price_details\": {\n            \"price\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n            \"product\": \"prod_S8bnyI5qmG5mEz\"\n          },\n          \"type\": \"price_details\",\n          \"unit_amount_decimal\": \"4\"\n        },\n        \"proration\": false,\n        \"proration_details\": {\n          \"credited_items\": null\n        },\n        \"quantity\": 0,\n        \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n        \"subscription_item\": \"si_SR36dgFQ0BkALS\",\n        \"tax_amounts\": [\n          {\n            \"amount\": 0,\n            \"inclusive\": true,\n            \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0\n          }\n        ],\n        \"tax_rates\": [],\n        \"taxes\": [\n          {\n            \"amount\": 0,\n            \"tax_behavior\": \"inclusive\",\n            \"tax_rate_details\": {\n              \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n            },\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0,\n            \"type\": \"tax_rate_details\"\n          }\n        ],\n        \"type\": \"subscription\",\n        \"unit_amount_excluding_tax\": null\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/invoices/in_1RWB09IWVkWpNCp7DTky7Q3E/lines\"\n  },\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_payment_attempt\": null,\n  \"number\": \"B3B25542-0006\",\n  \"on_behalf_of\": null,\n  \"paid\": true,\n  \"paid_out_of_band\": false,\n  \"parent\": {\n    \"quote_details\": null,\n    \"subscription_details\": {\n      \"metadata\": {},\n      \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\"\n    },\n    \"type\": \"subscription_details\"\n  },\n  \"payment_intent\": null,\n  \"payment_settings\": {\n    \"default_mandate\": null,\n    \"payment_method_options\": null,\n    \"payment_method_types\": null\n  },\n  \"period_end\": **********,\n  \"period_start\": **********,\n  \"post_payment_credit_notes_amount\": 0,\n  \"pre_payment_credit_notes_amount\": 0,\n  \"quote\": null,\n  \"receipt_number\": null,\n  \"rendering\": null,\n  \"rendering_options\": null,\n  \"shipping_cost\": null,\n  \"shipping_details\": null,\n  \"starting_balance\": 0,\n  \"statement_descriptor\": null,\n  \"status\": \"paid\",\n  \"status_transitions\": {\n    \"finalized_at\": **********,\n    \"marked_uncollectible_at\": null,\n    \"paid_at\": **********,\n    \"voided_at\": null\n  },\n  \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n  \"subscription_details\": {\n    \"metadata\": {}\n  },\n  \"subtotal\": 0,\n  \"subtotal_excluding_tax\": 0,\n  \"tax\": 0,\n  \"test_clock\": null,\n  \"total\": 0,\n  \"total_discount_amounts\": [],\n  \"total_excluding_tax\": 0,\n  \"total_pretax_credit_amounts\": [],\n  \"total_tax_amounts\": [\n    {\n      \"amount\": 0,\n      \"inclusive\": true,\n      \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0\n    }\n  ],\n  \"total_taxes\": [\n    {\n      \"amount\": 0,\n      \"tax_behavior\": \"inclusive\",\n      \"tax_rate_details\": {\n        \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n      },\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0,\n      \"type\": \"tax_rate_details\"\n    }\n  ],\n  \"transfer_data\": null,\n  \"webhooks_delivered_at\": 1749019243\n}", "headers": {"Server": "nginx", "Date": "Thu, 05 Jun 2025 06:35:23 GMT", "Content-Type": "application/json", "Content-Length": "8121", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=g2eZfiz_eC1F8DaioSFyixjK8sNgPCJcF-FGigp6WKbZUyYLKNF925PZbPH5Cbb_YVRy8rTOI8iYPZdj", "Request-Id": "req_L8KeAYKhyzzyws", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/invoices/upcoming?subscription=sub_1RWB09IWVkWpNCp7q9RhQRt2", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"object\": \"invoice\",\n  \"account_country\": \"AU\",\n  \"account_name\": \"Everfree Pty Ltd\",\n  \"account_tax_ids\": null,\n  \"amount_due\": 0,\n  \"amount_overpaid\": 0,\n  \"amount_paid\": 0,\n  \"amount_remaining\": 0,\n  \"amount_shipping\": 0,\n  \"application\": null,\n  \"application_fee_amount\": null,\n  \"attempt_count\": 0,\n  \"attempted\": false,\n  \"automatic_tax\": {\n    \"disabled_reason\": null,\n    \"enabled\": true,\n    \"liability\": {\n      \"type\": \"self\"\n    },\n    \"provider\": \"stripe\",\n    \"status\": \"complete\"\n  },\n  \"automatically_finalizes_at\": null,\n  \"billing_reason\": \"upcoming\",\n  \"charge\": null,\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"custom_fields\": null,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"customer_address\": {\n    \"city\": \"\",\n    \"country\": \"AU\",\n    \"line1\": \"\",\n    \"line2\": \"\",\n    \"postal_code\": \"\",\n    \"state\": \"\"\n  },\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_name\": \"<DO NOT DELETE> Petal Pro Test User\",\n  \"customer_phone\": null,\n  \"customer_shipping\": {\n    \"address\": {\n      \"city\": \"\",\n      \"country\": \"AU\",\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"postal_code\": \"\",\n      \"state\": \"\"\n    },\n    \"name\": \"<DO NOT DELETE> Petal Pro Test User\",\n    \"phone\": \"\"\n  },\n  \"customer_tax_exempt\": \"none\",\n  \"customer_tax_ids\": [],\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"discounts\": [],\n  \"due_date\": null,\n  \"effective_at\": null,\n  \"ending_balance\": 0,\n  \"footer\": null,\n  \"from_invoice\": null,\n  \"issuer\": {\n    \"type\": \"self\"\n  },\n  \"last_finalization_error\": null,\n  \"latest_revision\": null,\n  \"lines\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"il_tmp_1b8136IWVkWpNCp7b445c213\",\n        \"object\": \"line_item\",\n        \"amount\": 0,\n        \"amount_excluding_tax\": 0,\n        \"currency\": \"aud\",\n        \"description\": \"0 × Admin AI (at $0.04 per 100 units / month)\",\n        \"discount_amounts\": [],\n        \"discountable\": true,\n        \"discounts\": [],\n        \"invoice\": null,\n        \"livemode\": false,\n        \"metadata\": {},\n        \"parent\": {\n          \"invoice_item_details\": null,\n          \"subscription_item_details\": {\n            \"invoice_item\": null,\n            \"proration\": false,\n            \"proration_details\": {\n              \"credited_items\": null\n            },\n            \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n            \"subscription_item\": \"si_SR36dgFQ0BkALS\"\n          },\n          \"type\": \"subscription_item_details\"\n        },\n        \"period\": {\n          \"end\": 1751611200,\n          \"start\": **********\n        },\n        \"plan\": {\n          \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 4,\n          \"amount_decimal\": \"4\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n          \"nickname\": null,\n          \"product\": \"prod_S8bnyI5qmG5mEz\",\n          \"tiers_mode\": null,\n          \"transform_usage\": {\n            \"divide_by\": 100,\n            \"round\": \"up\"\n          },\n          \"trial_period_days\": null,\n          \"usage_type\": \"metered\"\n        },\n        \"pretax_credit_amounts\": [],\n        \"price\": {\n          \"id\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_S8bnyI5qmG5mEz\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"meter\": \"mtr_test_61SNiSGOruDKkjzQ241IWVkWpNCp70L2\",\n            \"trial_period_days\": null,\n            \"usage_type\": \"metered\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": {\n            \"divide_by\": 100,\n            \"round\": \"up\"\n          },\n          \"type\": \"recurring\",\n          \"unit_amount\": 4,\n          \"unit_amount_decimal\": \"4\"\n        },\n        \"pricing\": {\n          \"price_details\": {\n            \"price\": \"price_1REKZxIWVkWpNCp7Otf1hyEx\",\n            \"product\": \"prod_S8bnyI5qmG5mEz\"\n          },\n          \"type\": \"price_details\",\n          \"unit_amount_decimal\": \"4\"\n        },\n        \"proration\": false,\n        \"proration_details\": {\n          \"credited_items\": null\n        },\n        \"quantity\": 0,\n        \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n        \"subscription_item\": \"si_SR36dgFQ0BkALS\",\n        \"tax_amounts\": [\n          {\n            \"amount\": 0,\n            \"inclusive\": true,\n            \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0\n          }\n        ],\n        \"tax_rates\": [],\n        \"taxes\": [\n          {\n            \"amount\": 0,\n            \"tax_behavior\": \"inclusive\",\n            \"tax_rate_details\": {\n              \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n            },\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0,\n            \"type\": \"tax_rate_details\"\n          }\n        ],\n        \"type\": \"subscription\",\n        \"unit_amount_excluding_tax\": null\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/invoices/upcoming/lines?subscription=sub_1RWB09IWVkWpNCp7q9RhQRt2\"\n  },\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_payment_attempt\": 1751614841,\n  \"number\": null,\n  \"on_behalf_of\": null,\n  \"paid\": false,\n  \"paid_out_of_band\": false,\n  \"parent\": {\n    \"quote_details\": null,\n    \"subscription_details\": {\n      \"metadata\": {},\n      \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\"\n    },\n    \"type\": \"subscription_details\"\n  },\n  \"payment_intent\": null,\n  \"payment_settings\": {\n    \"default_mandate\": null,\n    \"payment_method_options\": null,\n    \"payment_method_types\": null\n  },\n  \"period_end\": **********,\n  \"period_start\": **********,\n  \"post_payment_credit_notes_amount\": 0,\n  \"pre_payment_credit_notes_amount\": 0,\n  \"quote\": null,\n  \"receipt_number\": null,\n  \"rendering\": null,\n  \"rendering_options\": null,\n  \"shipping_cost\": null,\n  \"shipping_details\": null,\n  \"starting_balance\": 0,\n  \"statement_descriptor\": null,\n  \"status\": \"draft\",\n  \"status_transitions\": {\n    \"finalized_at\": null,\n    \"marked_uncollectible_at\": null,\n    \"paid_at\": null,\n    \"voided_at\": null\n  },\n  \"subscription\": \"sub_1RWB09IWVkWpNCp7q9RhQRt2\",\n  \"subscription_details\": {\n    \"metadata\": {}\n  },\n  \"subtotal\": 0,\n  \"subtotal_excluding_tax\": 0,\n  \"tax\": 0,\n  \"test_clock\": null,\n  \"total\": 0,\n  \"total_discount_amounts\": [],\n  \"total_excluding_tax\": 0,\n  \"total_pretax_credit_amounts\": [],\n  \"total_tax_amounts\": [\n    {\n      \"amount\": 0,\n      \"inclusive\": true,\n      \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0\n    }\n  ],\n  \"total_taxes\": [\n    {\n      \"amount\": 0,\n      \"tax_behavior\": \"inclusive\",\n      \"tax_rate_details\": {\n        \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n      },\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0,\n      \"type\": \"tax_rate_details\"\n    }\n  ],\n  \"transfer_data\": null,\n  \"webhooks_delivered_at\": null\n}", "headers": {"Server": "nginx", "Date": "Thu, 05 Jun 2025 06:35:24 GMT", "Content-Type": "application/json", "Content-Length": "7649", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=g2eZfiz_eC1F8DaioSFyixjK8sNgPCJcF-FGigp6WKbZUyYLKNF925PZbPH5Cbb_YVRy8rTOI8iYPZdj", "Request-Id": "req_hz6ZjLv0sR19nU", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}]