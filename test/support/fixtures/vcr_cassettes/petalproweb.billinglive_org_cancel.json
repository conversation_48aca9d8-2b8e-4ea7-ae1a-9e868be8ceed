[{"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/subscriptions/sub_1RJX2hIWVkWpNCp7ztXBM8Cl", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n  \"object\": \"subscription\",\n  \"application\": null,\n  \"application_fee_percent\": null,\n  \"automatic_tax\": {\n    \"disabled_reason\": null,\n    \"enabled\": true,\n    \"liability\": {\n      \"type\": \"self\"\n    }\n  },\n  \"billing_cycle_anchor\": **********,\n  \"billing_cycle_anchor_config\": null,\n  \"billing_thresholds\": null,\n  \"cancel_at\": null,\n  \"cancel_at_period_end\": false,\n  \"canceled_at\": null,\n  \"cancellation_details\": {\n    \"comment\": null,\n    \"feedback\": null,\n    \"reason\": null\n  },\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"current_period_end\": **********,\n  \"current_period_start\": **********,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"days_until_due\": null,\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"discounts\": [],\n  \"ended_at\": null,\n  \"invoice_settings\": {\n    \"account_tax_ids\": null,\n    \"issuer\": {\n      \"type\": \"self\"\n    }\n  },\n  \"items\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"si_SDz0XXgn0Zwa3X\",\n        \"object\": \"subscription_item\",\n        \"billing_thresholds\": null,\n        \"created\": **********,\n        \"current_period_end\": **********,\n        \"current_period_start\": **********,\n        \"discounts\": [],\n        \"metadata\": {},\n        \"plan\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 199,\n          \"amount_decimal\": \"199\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"meter\": null,\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"tiers_mode\": null,\n          \"transform_usage\": null,\n          \"trial_period_days\": null,\n          \"usage_type\": \"licensed\"\n        },\n        \"price\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"meter\": null,\n            \"trial_period_days\": null,\n            \"usage_type\": \"licensed\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": null,\n          \"type\": \"recurring\",\n          \"unit_amount\": 199,\n          \"unit_amount_decimal\": \"199\"\n        },\n        \"quantity\": 1,\n        \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n        \"tax_rates\": []\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/subscription_items?subscription=sub_1RJX2hIWVkWpNCp7ztXBM8Cl\"\n  },\n  \"latest_invoice\": \"in_1RJX2iIWVkWpNCp7Tx9L3ykm\",\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_pending_invoice_item_invoice\": null,\n  \"on_behalf_of\": null,\n  \"pause_collection\": null,\n  \"payment_settings\": {\n    \"payment_method_options\": null,\n    \"payment_method_types\": null,\n    \"save_default_payment_method\": \"off\"\n  },\n  \"pending_invoice_item_interval\": null,\n  \"pending_setup_intent\": null,\n  \"pending_update\": null,\n  \"plan\": {\n    \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n    \"object\": \"plan\",\n    \"active\": true,\n    \"aggregate_usage\": null,\n    \"amount\": 199,\n    \"amount_decimal\": \"199\",\n    \"billing_scheme\": \"per_unit\",\n    \"created\": **********,\n    \"currency\": \"aud\",\n    \"interval\": \"month\",\n    \"interval_count\": 1,\n    \"livemode\": false,\n    \"metadata\": {},\n    \"meter\": null,\n    \"nickname\": null,\n    \"product\": \"prod_PFDZyFfhgGUNOg\",\n    \"tiers_mode\": null,\n    \"transform_usage\": null,\n    \"trial_period_days\": null,\n    \"usage_type\": \"licensed\"\n  },\n  \"quantity\": 1,\n  \"schedule\": null,\n  \"start_date\": **********,\n  \"status\": \"active\",\n  \"test_clock\": null,\n  \"transfer_data\": null,\n  \"trial_end\": null,\n  \"trial_settings\": {\n    \"end_behavior\": {\n      \"missing_payment_method\": \"create_invoice\"\n    }\n  },\n  \"trial_start\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:37:34 GMT", "Content-Type": "application/json", "Content-Length": "4428", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=MTG_1MqQLYqNJXvs5nqh-9w0MvF6D6_8l7Hk0RXjpJMhw-Kq70iSLONsQzVNF-Ok926sNHhj7exQgwhU", "Request-Id": "req_kV8rjQe8zlxx71", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/invoices?limit=2&subscription=sub_1RJX2hIWVkWpNCp7ztXBM8Cl", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"object\": \"list\",\n  \"data\": [\n    {\n      \"id\": \"in_1RJX2iIWVkWpNCp7Tx9L3ykm\",\n      \"object\": \"invoice\",\n      \"account_country\": \"AU\",\n      \"account_name\": \"Everfree Pty Ltd\",\n      \"account_tax_ids\": null,\n      \"amount_due\": 199,\n      \"amount_overpaid\": 0,\n      \"amount_paid\": 199,\n      \"amount_remaining\": 0,\n      \"amount_shipping\": 0,\n      \"application\": null,\n      \"application_fee_amount\": null,\n      \"attempt_count\": 1,\n      \"attempted\": true,\n      \"auto_advance\": false,\n      \"automatic_tax\": {\n        \"disabled_reason\": null,\n        \"enabled\": true,\n        \"liability\": {\n          \"type\": \"self\"\n        },\n        \"provider\": \"stripe\",\n        \"status\": \"complete\"\n      },\n      \"automatically_finalizes_at\": null,\n      \"billing_reason\": \"subscription_create\",\n      \"charge\": \"ch_3RJX2iIWVkWpNCp72vl43Ztu\",\n      \"collection_method\": \"charge_automatically\",\n      \"created\": **********,\n      \"currency\": \"aud\",\n      \"custom_fields\": null,\n      \"customer\": \"cus_PFDXVnCJHqastp\",\n      \"customer_address\": {\n        \"city\": \"\",\n        \"country\": \"AU\",\n        \"line1\": \"\",\n        \"line2\": \"\",\n        \"postal_code\": \"\",\n        \"state\": \"\"\n      },\n      \"customer_email\": \"<EMAIL>\",\n      \"customer_name\": \"<DO NOT DELETE> Petal Pro Test User\",\n      \"customer_phone\": null,\n      \"customer_shipping\": {\n        \"address\": {\n          \"city\": \"\",\n          \"country\": \"AU\",\n          \"line1\": \"\",\n          \"line2\": \"\",\n          \"postal_code\": \"\",\n          \"state\": \"\"\n        },\n        \"name\": \"<DO NOT DELETE> Petal Pro Test User\",\n        \"phone\": \"\"\n      },\n      \"customer_tax_exempt\": \"none\",\n      \"customer_tax_ids\": [],\n      \"default_payment_method\": null,\n      \"default_source\": null,\n      \"default_tax_rates\": [],\n      \"description\": null,\n      \"discount\": null,\n      \"discounts\": [],\n      \"due_date\": null,\n      \"effective_at\": **********,\n      \"ending_balance\": 0,\n      \"footer\": null,\n      \"from_invoice\": null,\n      \"hosted_invoice_url\": \"https://invoice.stripe.com/i/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TRHowbXRXRTFmdW1QR3Q1MlBKR0xqWFU4YXFvcWg0LDEzNjU0NjY1NA0200iBLgmunk?s=ap\",\n      \"invoice_pdf\": \"https://pay.stripe.com/invoice/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TRHowbXRXRTFmdW1QR3Q1MlBKR0xqWFU4YXFvcWg0LDEzNjU0NjY1NA0200iBLgmunk/pdf?s=ap\",\n      \"issuer\": {\n        \"type\": \"self\"\n      },\n      \"last_finalization_error\": null,\n      \"latest_revision\": null,\n      \"lines\": {\n        \"object\": \"list\",\n        \"data\": [\n          {\n            \"id\": \"il_1RJX2iIWVkWpNCp7io7jji4y\",\n            \"object\": \"line_item\",\n            \"amount\": 199,\n            \"amount_excluding_tax\": 199,\n            \"currency\": \"aud\",\n            \"description\": \"1 × <DO NOT DELETE> Petal Pro Test Plan A (at $1.99 / month)\",\n            \"discount_amounts\": [],\n            \"discountable\": true,\n            \"discounts\": [],\n            \"invoice\": \"in_1RJX2iIWVkWpNCp7Tx9L3ykm\",\n            \"livemode\": false,\n            \"metadata\": {},\n            \"parent\": {\n              \"invoice_item_details\": null,\n              \"subscription_item_details\": {\n                \"invoice_item\": null,\n                \"proration\": false,\n                \"proration_details\": {\n                  \"credited_items\": null\n                },\n                \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n                \"subscription_item\": \"si_SDz0XXgn0Zwa3X\"\n              },\n              \"type\": \"subscription_item_details\"\n            },\n            \"period\": {\n              \"end\": **********,\n              \"start\": **********\n            },\n            \"plan\": {\n              \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n              \"object\": \"plan\",\n              \"active\": true,\n              \"aggregate_usage\": null,\n              \"amount\": 199,\n              \"amount_decimal\": \"199\",\n              \"billing_scheme\": \"per_unit\",\n              \"created\": **********,\n              \"currency\": \"aud\",\n              \"interval\": \"month\",\n              \"interval_count\": 1,\n              \"livemode\": false,\n              \"metadata\": {},\n              \"meter\": null,\n              \"nickname\": null,\n              \"product\": \"prod_PFDZyFfhgGUNOg\",\n              \"tiers_mode\": null,\n              \"transform_usage\": null,\n              \"trial_period_days\": null,\n              \"usage_type\": \"licensed\"\n            },\n            \"pretax_credit_amounts\": [],\n            \"price\": {\n              \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n              \"object\": \"price\",\n              \"active\": true,\n              \"billing_scheme\": \"per_unit\",\n              \"created\": **********,\n              \"currency\": \"aud\",\n              \"custom_unit_amount\": null,\n              \"livemode\": false,\n              \"lookup_key\": null,\n              \"metadata\": {},\n              \"nickname\": null,\n              \"product\": \"prod_PFDZyFfhgGUNOg\",\n              \"recurring\": {\n                \"aggregate_usage\": null,\n                \"interval\": \"month\",\n                \"interval_count\": 1,\n                \"meter\": null,\n                \"trial_period_days\": null,\n                \"usage_type\": \"licensed\"\n              },\n              \"tax_behavior\": \"inclusive\",\n              \"tiers_mode\": null,\n              \"transform_quantity\": null,\n              \"type\": \"recurring\",\n              \"unit_amount\": 199,\n              \"unit_amount_decimal\": \"199\"\n            },\n            \"pricing\": {\n              \"price_details\": {\n                \"price\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n                \"product\": \"prod_PFDZyFfhgGUNOg\"\n              },\n              \"type\": \"price_details\",\n              \"unit_amount_decimal\": \"199\"\n            },\n            \"proration\": false,\n            \"proration_details\": {\n              \"credited_items\": null\n            },\n            \"quantity\": 1,\n            \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n            \"subscription_item\": \"si_SDz0XXgn0Zwa3X\",\n            \"tax_amounts\": [\n              {\n                \"amount\": 0,\n                \"inclusive\": true,\n                \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n                \"taxability_reason\": \"not_collecting\",\n                \"taxable_amount\": 0\n              }\n            ],\n            \"tax_rates\": [],\n            \"taxes\": [\n              {\n                \"amount\": 0,\n                \"tax_behavior\": \"inclusive\",\n                \"tax_rate_details\": {\n                  \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n                },\n                \"taxability_reason\": \"not_collecting\",\n                \"taxable_amount\": 0,\n                \"type\": \"tax_rate_details\"\n              }\n            ],\n            \"type\": \"subscription\",\n            \"unit_amount_excluding_tax\": \"199\"\n          }\n        ],\n        \"has_more\": false,\n        \"total_count\": 1,\n        \"url\": \"/v1/invoices/in_1RJX2iIWVkWpNCp7Tx9L3ykm/lines\"\n      },\n      \"livemode\": false,\n      \"metadata\": {},\n      \"next_payment_attempt\": null,\n      \"number\": \"B3B25542-0004\",\n      \"on_behalf_of\": null,\n      \"paid\": true,\n      \"paid_out_of_band\": false,\n      \"parent\": {\n        \"quote_details\": null,\n        \"subscription_details\": {\n          \"metadata\": {},\n          \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\"\n        },\n        \"type\": \"subscription_details\"\n      },\n      \"payment_intent\": \"pi_3RJX2iIWVkWpNCp72OOt4grQ\",\n      \"payment_settings\": {\n        \"default_mandate\": null,\n        \"payment_method_options\": null,\n        \"payment_method_types\": null\n      },\n      \"period_end\": **********,\n      \"period_start\": **********,\n      \"post_payment_credit_notes_amount\": 0,\n      \"pre_payment_credit_notes_amount\": 0,\n      \"quote\": null,\n      \"receipt_number\": null,\n      \"rendering\": null,\n      \"rendering_options\": null,\n      \"shipping_cost\": null,\n      \"shipping_details\": null,\n      \"starting_balance\": 0,\n      \"statement_descriptor\": null,\n      \"status\": \"paid\",\n      \"status_transitions\": {\n        \"finalized_at\": **********,\n        \"marked_uncollectible_at\": null,\n        \"paid_at\": **********,\n        \"voided_at\": null\n      },\n      \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n      \"subscription_details\": {\n        \"metadata\": {}\n      },\n      \"subtotal\": 199,\n      \"subtotal_excluding_tax\": 199,\n      \"tax\": 0,\n      \"test_clock\": null,\n      \"total\": 199,\n      \"total_discount_amounts\": [],\n      \"total_excluding_tax\": 199,\n      \"total_pretax_credit_amounts\": [],\n      \"total_tax_amounts\": [\n        {\n          \"amount\": 0,\n          \"inclusive\": true,\n          \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n          \"taxability_reason\": \"not_collecting\",\n          \"taxable_amount\": 0\n        }\n      ],\n      \"total_taxes\": [\n        {\n          \"amount\": 0,\n          \"tax_behavior\": \"inclusive\",\n          \"tax_rate_details\": {\n            \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n          },\n          \"taxability_reason\": \"not_collecting\",\n          \"taxable_amount\": 0,\n          \"type\": \"tax_rate_details\"\n        }\n      ],\n      \"transfer_data\": null,\n      \"webhooks_delivered_at\": 1746005708\n    }\n  ],\n  \"has_more\": false,\n  \"url\": \"/v1/invoices\"\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:37:35 GMT", "Content-Type": "application/json", "Content-Length": "9217", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=MTG_1MqQLYqNJXvs5nqh-9w0MvF6D6_8l7Hk0RXjpJMhw-Kq70iSLONsQzVNF-Ok926sNHhj7exQgwhU", "Request-Id": "req_rQBIjEUYaRWnob", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/invoices/in_1RJX2iIWVkWpNCp7Tx9L3ykm", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"in_1RJX2iIWVkWpNCp7Tx9L3ykm\",\n  \"object\": \"invoice\",\n  \"account_country\": \"AU\",\n  \"account_name\": \"Everfree Pty Ltd\",\n  \"account_tax_ids\": null,\n  \"amount_due\": 199,\n  \"amount_overpaid\": 0,\n  \"amount_paid\": 199,\n  \"amount_remaining\": 0,\n  \"amount_shipping\": 0,\n  \"application\": null,\n  \"application_fee_amount\": null,\n  \"attempt_count\": 1,\n  \"attempted\": true,\n  \"auto_advance\": false,\n  \"automatic_tax\": {\n    \"disabled_reason\": null,\n    \"enabled\": true,\n    \"liability\": {\n      \"type\": \"self\"\n    },\n    \"provider\": \"stripe\",\n    \"status\": \"complete\"\n  },\n  \"automatically_finalizes_at\": null,\n  \"billing_reason\": \"subscription_create\",\n  \"charge\": \"ch_3RJX2iIWVkWpNCp72vl43Ztu\",\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"custom_fields\": null,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"customer_address\": {\n    \"city\": \"\",\n    \"country\": \"AU\",\n    \"line1\": \"\",\n    \"line2\": \"\",\n    \"postal_code\": \"\",\n    \"state\": \"\"\n  },\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_name\": \"<DO NOT DELETE> Petal Pro Test User\",\n  \"customer_phone\": null,\n  \"customer_shipping\": {\n    \"address\": {\n      \"city\": \"\",\n      \"country\": \"AU\",\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"postal_code\": \"\",\n      \"state\": \"\"\n    },\n    \"name\": \"<DO NOT DELETE> Petal Pro Test User\",\n    \"phone\": \"\"\n  },\n  \"customer_tax_exempt\": \"none\",\n  \"customer_tax_ids\": [],\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"discounts\": [],\n  \"due_date\": null,\n  \"effective_at\": **********,\n  \"ending_balance\": 0,\n  \"footer\": null,\n  \"from_invoice\": null,\n  \"hosted_invoice_url\": \"https://invoice.stripe.com/i/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TRHowbXRXRTFmdW1QR3Q1MlBKR0xqWFU4YXFvcWg0LDEzNjU0NjY1NQ0200rj5eKEHL?s=ap\",\n  \"invoice_pdf\": \"https://pay.stripe.com/invoice/acct_1KQNyUIWVkWpNCp7/test_YWNjdF8xS1FOeVVJV1ZrV3BOQ3A3LF9TRHowbXRXRTFmdW1QR3Q1MlBKR0xqWFU4YXFvcWg0LDEzNjU0NjY1NQ0200rj5eKEHL/pdf?s=ap\",\n  \"issuer\": {\n    \"type\": \"self\"\n  },\n  \"last_finalization_error\": null,\n  \"latest_revision\": null,\n  \"lines\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"il_1RJX2iIWVkWpNCp7io7jji4y\",\n        \"object\": \"line_item\",\n        \"amount\": 199,\n        \"amount_excluding_tax\": 199,\n        \"currency\": \"aud\",\n        \"description\": \"1 × <DO NOT DELETE> Petal Pro Test Plan A (at $1.99 / month)\",\n        \"discount_amounts\": [],\n        \"discountable\": true,\n        \"discounts\": [],\n        \"invoice\": \"in_1RJX2iIWVkWpNCp7Tx9L3ykm\",\n        \"livemode\": false,\n        \"metadata\": {},\n        \"parent\": {\n          \"invoice_item_details\": null,\n          \"subscription_item_details\": {\n            \"invoice_item\": null,\n            \"proration\": false,\n            \"proration_details\": {\n              \"credited_items\": null\n            },\n            \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n            \"subscription_item\": \"si_SDz0XXgn0Zwa3X\"\n          },\n          \"type\": \"subscription_item_details\"\n        },\n        \"period\": {\n          \"end\": **********,\n          \"start\": **********\n        },\n        \"plan\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 199,\n          \"amount_decimal\": \"199\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"meter\": null,\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"tiers_mode\": null,\n          \"transform_usage\": null,\n          \"trial_period_days\": null,\n          \"usage_type\": \"licensed\"\n        },\n        \"pretax_credit_amounts\": [],\n        \"price\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"meter\": null,\n            \"trial_period_days\": null,\n            \"usage_type\": \"licensed\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": null,\n          \"type\": \"recurring\",\n          \"unit_amount\": 199,\n          \"unit_amount_decimal\": \"199\"\n        },\n        \"pricing\": {\n          \"price_details\": {\n            \"price\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n            \"product\": \"prod_PFDZyFfhgGUNOg\"\n          },\n          \"type\": \"price_details\",\n          \"unit_amount_decimal\": \"199\"\n        },\n        \"proration\": false,\n        \"proration_details\": {\n          \"credited_items\": null\n        },\n        \"quantity\": 1,\n        \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n        \"subscription_item\": \"si_SDz0XXgn0Zwa3X\",\n        \"tax_amounts\": [\n          {\n            \"amount\": 0,\n            \"inclusive\": true,\n            \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0\n          }\n        ],\n        \"tax_rates\": [],\n        \"taxes\": [\n          {\n            \"amount\": 0,\n            \"tax_behavior\": \"inclusive\",\n            \"tax_rate_details\": {\n              \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n            },\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0,\n            \"type\": \"tax_rate_details\"\n          }\n        ],\n        \"type\": \"subscription\",\n        \"unit_amount_excluding_tax\": \"199\"\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/invoices/in_1RJX2iIWVkWpNCp7Tx9L3ykm/lines\"\n  },\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_payment_attempt\": null,\n  \"number\": \"B3B25542-0004\",\n  \"on_behalf_of\": null,\n  \"paid\": true,\n  \"paid_out_of_band\": false,\n  \"parent\": {\n    \"quote_details\": null,\n    \"subscription_details\": {\n      \"metadata\": {},\n      \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\"\n    },\n    \"type\": \"subscription_details\"\n  },\n  \"payment_intent\": \"pi_3RJX2iIWVkWpNCp72OOt4grQ\",\n  \"payment_settings\": {\n    \"default_mandate\": null,\n    \"payment_method_options\": null,\n    \"payment_method_types\": null\n  },\n  \"period_end\": **********,\n  \"period_start\": **********,\n  \"post_payment_credit_notes_amount\": 0,\n  \"pre_payment_credit_notes_amount\": 0,\n  \"quote\": null,\n  \"receipt_number\": null,\n  \"rendering\": null,\n  \"rendering_options\": null,\n  \"shipping_cost\": null,\n  \"shipping_details\": null,\n  \"starting_balance\": 0,\n  \"statement_descriptor\": null,\n  \"status\": \"paid\",\n  \"status_transitions\": {\n    \"finalized_at\": **********,\n    \"marked_uncollectible_at\": null,\n    \"paid_at\": **********,\n    \"voided_at\": null\n  },\n  \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n  \"subscription_details\": {\n    \"metadata\": {}\n  },\n  \"subtotal\": 199,\n  \"subtotal_excluding_tax\": 199,\n  \"tax\": 0,\n  \"test_clock\": null,\n  \"total\": 199,\n  \"total_discount_amounts\": [],\n  \"total_excluding_tax\": 199,\n  \"total_pretax_credit_amounts\": [],\n  \"total_tax_amounts\": [\n    {\n      \"amount\": 0,\n      \"inclusive\": true,\n      \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0\n    }\n  ],\n  \"total_taxes\": [\n    {\n      \"amount\": 0,\n      \"tax_behavior\": \"inclusive\",\n      \"tax_rate_details\": {\n        \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n      },\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0,\n      \"type\": \"tax_rate_details\"\n    }\n  ],\n  \"transfer_data\": null,\n  \"webhooks_delivered_at\": 1746005708\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:37:35 GMT", "Content-Type": "application/json", "Content-Length": "8007", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=MTG_1MqQLYqNJXvs5nqh-9w0MvF6D6_8l7Hk0RXjpJMhw-Kq70iSLONsQzVNF-Ok926sNHhj7exQgwhU", "Request-Id": "req_d7Znx9AiLHkCz3", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/invoices/upcoming?subscription=sub_1RJX2hIWVkWpNCp7ztXBM8Cl", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"object\": \"invoice\",\n  \"account_country\": \"AU\",\n  \"account_name\": \"Everfree Pty Ltd\",\n  \"account_tax_ids\": null,\n  \"amount_due\": 199,\n  \"amount_overpaid\": 0,\n  \"amount_paid\": 0,\n  \"amount_remaining\": 199,\n  \"amount_shipping\": 0,\n  \"application\": null,\n  \"application_fee_amount\": null,\n  \"attempt_count\": 0,\n  \"attempted\": false,\n  \"automatic_tax\": {\n    \"disabled_reason\": null,\n    \"enabled\": true,\n    \"liability\": {\n      \"type\": \"self\"\n    },\n    \"provider\": \"stripe\",\n    \"status\": \"complete\"\n  },\n  \"automatically_finalizes_at\": null,\n  \"billing_reason\": \"upcoming\",\n  \"charge\": null,\n  \"collection_method\": \"charge_automatically\",\n  \"created\": **********,\n  \"currency\": \"aud\",\n  \"custom_fields\": null,\n  \"customer\": \"cus_PFDXVnCJHqastp\",\n  \"customer_address\": {\n    \"city\": \"\",\n    \"country\": \"AU\",\n    \"line1\": \"\",\n    \"line2\": \"\",\n    \"postal_code\": \"\",\n    \"state\": \"\"\n  },\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_name\": \"<DO NOT DELETE> Petal Pro Test User\",\n  \"customer_phone\": null,\n  \"customer_shipping\": {\n    \"address\": {\n      \"city\": \"\",\n      \"country\": \"AU\",\n      \"line1\": \"\",\n      \"line2\": \"\",\n      \"postal_code\": \"\",\n      \"state\": \"\"\n    },\n    \"name\": \"<DO NOT DELETE> Petal Pro Test User\",\n    \"phone\": \"\"\n  },\n  \"customer_tax_exempt\": \"none\",\n  \"customer_tax_ids\": [],\n  \"default_payment_method\": null,\n  \"default_source\": null,\n  \"default_tax_rates\": [],\n  \"description\": null,\n  \"discount\": null,\n  \"discounts\": [],\n  \"due_date\": null,\n  \"effective_at\": null,\n  \"ending_balance\": 0,\n  \"footer\": null,\n  \"from_invoice\": null,\n  \"issuer\": {\n    \"type\": \"self\"\n  },\n  \"last_finalization_error\": null,\n  \"latest_revision\": null,\n  \"lines\": {\n    \"object\": \"list\",\n    \"data\": [\n      {\n        \"id\": \"il_tmp_1ff4a8IWVkWpNCp7459f7288\",\n        \"object\": \"line_item\",\n        \"amount\": 199,\n        \"amount_excluding_tax\": 199,\n        \"currency\": \"aud\",\n        \"description\": \"1 × <DO NOT DELETE> Petal Pro Test Plan A (at $1.99 / month)\",\n        \"discount_amounts\": [],\n        \"discountable\": true,\n        \"discounts\": [],\n        \"invoice\": null,\n        \"livemode\": false,\n        \"metadata\": {},\n        \"parent\": {\n          \"invoice_item_details\": null,\n          \"subscription_item_details\": {\n            \"invoice_item\": null,\n            \"proration\": false,\n            \"proration_details\": {\n              \"credited_items\": null\n            },\n            \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n            \"subscription_item\": \"si_SDz0XXgn0Zwa3X\"\n          },\n          \"type\": \"subscription_item_details\"\n        },\n        \"period\": {\n          \"end\": 1751276103,\n          \"start\": **********\n        },\n        \"plan\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"plan\",\n          \"active\": true,\n          \"aggregate_usage\": null,\n          \"amount\": 199,\n          \"amount_decimal\": \"199\",\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"interval\": \"month\",\n          \"interval_count\": 1,\n          \"livemode\": false,\n          \"metadata\": {},\n          \"meter\": null,\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"tiers_mode\": null,\n          \"transform_usage\": null,\n          \"trial_period_days\": null,\n          \"usage_type\": \"licensed\"\n        },\n        \"pretax_credit_amounts\": [],\n        \"price\": {\n          \"id\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n          \"object\": \"price\",\n          \"active\": true,\n          \"billing_scheme\": \"per_unit\",\n          \"created\": **********,\n          \"currency\": \"aud\",\n          \"custom_unit_amount\": null,\n          \"livemode\": false,\n          \"lookup_key\": null,\n          \"metadata\": {},\n          \"nickname\": null,\n          \"product\": \"prod_PFDZyFfhgGUNOg\",\n          \"recurring\": {\n            \"aggregate_usage\": null,\n            \"interval\": \"month\",\n            \"interval_count\": 1,\n            \"meter\": null,\n            \"trial_period_days\": null,\n            \"usage_type\": \"licensed\"\n          },\n          \"tax_behavior\": \"inclusive\",\n          \"tiers_mode\": null,\n          \"transform_quantity\": null,\n          \"type\": \"recurring\",\n          \"unit_amount\": 199,\n          \"unit_amount_decimal\": \"199\"\n        },\n        \"pricing\": {\n          \"price_details\": {\n            \"price\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n            \"product\": \"prod_PFDZyFfhgGUNOg\"\n          },\n          \"type\": \"price_details\",\n          \"unit_amount_decimal\": \"199\"\n        },\n        \"proration\": false,\n        \"proration_details\": {\n          \"credited_items\": null\n        },\n        \"quantity\": 1,\n        \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n        \"subscription_item\": \"si_SDz0XXgn0Zwa3X\",\n        \"tax_amounts\": [\n          {\n            \"amount\": 0,\n            \"inclusive\": true,\n            \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0\n          }\n        ],\n        \"tax_rates\": [],\n        \"taxes\": [\n          {\n            \"amount\": 0,\n            \"tax_behavior\": \"inclusive\",\n            \"tax_rate_details\": {\n              \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n            },\n            \"taxability_reason\": \"not_collecting\",\n            \"taxable_amount\": 0,\n            \"type\": \"tax_rate_details\"\n          }\n        ],\n        \"type\": \"subscription\",\n        \"unit_amount_excluding_tax\": \"199\"\n      }\n    ],\n    \"has_more\": false,\n    \"total_count\": 1,\n    \"url\": \"/v1/invoices/upcoming/lines?subscription=sub_1RJX2hIWVkWpNCp7ztXBM8Cl\"\n  },\n  \"livemode\": false,\n  \"metadata\": {},\n  \"next_payment_attempt\": 1748601303,\n  \"number\": null,\n  \"on_behalf_of\": null,\n  \"paid\": false,\n  \"paid_out_of_band\": false,\n  \"parent\": {\n    \"quote_details\": null,\n    \"subscription_details\": {\n      \"metadata\": {},\n      \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\"\n    },\n    \"type\": \"subscription_details\"\n  },\n  \"payment_intent\": null,\n  \"payment_settings\": {\n    \"default_mandate\": null,\n    \"payment_method_options\": null,\n    \"payment_method_types\": null\n  },\n  \"period_end\": **********,\n  \"period_start\": **********,\n  \"post_payment_credit_notes_amount\": 0,\n  \"pre_payment_credit_notes_amount\": 0,\n  \"quote\": null,\n  \"receipt_number\": null,\n  \"rendering\": null,\n  \"rendering_options\": null,\n  \"shipping_cost\": null,\n  \"shipping_details\": null,\n  \"starting_balance\": 0,\n  \"statement_descriptor\": null,\n  \"status\": \"draft\",\n  \"status_transitions\": {\n    \"finalized_at\": null,\n    \"marked_uncollectible_at\": null,\n    \"paid_at\": null,\n    \"voided_at\": null\n  },\n  \"subscription\": \"sub_1RJX2hIWVkWpNCp7ztXBM8Cl\",\n  \"subscription_details\": {\n    \"metadata\": {}\n  },\n  \"subtotal\": 199,\n  \"subtotal_excluding_tax\": 199,\n  \"tax\": 0,\n  \"test_clock\": null,\n  \"total\": 199,\n  \"total_discount_amounts\": [],\n  \"total_excluding_tax\": 199,\n  \"total_pretax_credit_amounts\": [],\n  \"total_tax_amounts\": [\n    {\n      \"amount\": 0,\n      \"inclusive\": true,\n      \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\",\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0\n    }\n  ],\n  \"total_taxes\": [\n    {\n      \"amount\": 0,\n      \"tax_behavior\": \"inclusive\",\n      \"tax_rate_details\": {\n        \"tax_rate\": \"txr_1OQjGUIWVkWpNCp7VgpYAg1o\"\n      },\n      \"taxability_reason\": \"not_collecting\",\n      \"taxable_amount\": 0,\n      \"type\": \"tax_rate_details\"\n    }\n  ],\n  \"transfer_data\": null,\n  \"webhooks_delivered_at\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:37:35 GMT", "Content-Type": "application/json", "Content-Length": "7485", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=MTG_1MqQLYqNJXvs5nqh-9w0MvF6D6_8l7Hk0RXjpJMhw-Kq70iSLONsQzVNF-Ok926sNHhj7exQgwhU", "Request-Id": "req_bkn2q9BwiFSVEM", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}, {"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "", "url": "https://api.stripe.com/v1/products/prod_PFDZyFfhgGUNOg", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "get", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"id\": \"prod_PFDZyFfhgGUNOg\",\n  \"object\": \"product\",\n  \"active\": true,\n  \"attributes\": [],\n  \"created\": **********,\n  \"default_price\": \"price_1OQj8TIWVkWpNCp7ZlUSOaI9\",\n  \"description\": null,\n  \"features\": [],\n  \"images\": [],\n  \"livemode\": false,\n  \"marketing_features\": [],\n  \"metadata\": {},\n  \"name\": \"<DO NOT DELETE> Petal Pro Test Plan A\",\n  \"package_dimensions\": null,\n  \"shippable\": null,\n  \"statement_descriptor\": null,\n  \"tax_code\": \"txcd_10202000\",\n  \"type\": \"service\",\n  \"unit_label\": null,\n  \"updated\": 1703391809,\n  \"url\": null\n}", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:37:36 GMT", "Content-Type": "application/json", "Content-Length": "544", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=MTG_1MqQLYqNJXvs5nqh-9w0MvF6D6_8l7Hk0RXjpJMhw-Kq70iSLONsQzVNF-Ok926sNHhj7exQgwhU", "Request-Id": "req_I5vM0eWeQVA4ds", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 200}}]