[{"request": {"options": {"pool": "Elixir.Stripe.API", "with_body": "true"}, "body": "timestamp=1746005878&identifier=id_172&event_name=nonexistent_meter&payload%5Bvalue%5D=1&payload%5Bstripe_customer_id%5D=cus_Ra3JUjRxELazGZ", "url": "https://api.stripe.com/v1/billing/meter_events", "headers": {"Accept": "application/json; charset=utf8", "Accept-Encoding": "gzip", "Authorization": "***", "Connection": "keep-alive", "Content-Type": "application/x-www-form-urlencoded", "Idempotency-Key": "30tgu63s4ofhh54ks0000813", "Stripe-Version": "2022-11-15", "User-Agent": "Stripe/v1 stripe-elixir/2022-11-15"}, "method": "post", "request_body": ""}, "response": {"binary": false, "type": "ok", "body": "{\n  \"error\": {\n    \"message\": \"No active meter found for event name \\\"nonexistent_meter\\\".\",\n    \"request_log_url\": \"https://dashboard.stripe.com/test/logs/req_nMEF98Jn5B4gjw?t=1746005878\",\n    \"type\": \"invalid_request_error\"\n  }\n}\n", "headers": {"Server": "nginx", "Date": "Wed, 30 Apr 2025 09:37:58 GMT", "Content-Type": "application/json", "Content-Length": "232", "Connection": "keep-alive", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, HEAD, PUT, PATCH, POST, DELETE", "Access-Control-Allow-Origin": "*", "Access-Control-Expose-Headers": "Request-Id, Stripe-Manage-Version, Stripe-Should-<PERSON><PERSON>, X-Stripe-External-Auth-Required, X-Stripe-Privileged-Session-Required", "Access-Control-Max-Age": "300", "Cache-Control": "no-cache, no-store", "Content-Security-Policy": "base-uri 'none'; default-src 'none'; form-action 'none'; frame-ancestors 'none'; img-src 'self'; script-src 'self' 'report-sample'; style-src 'self'; worker-src 'none'; upgrade-insecure-requests; report-uri https://q.stripe.com/csp-violation?q=2xHZg5FzUbz83wdoMrntlkT6ehT4IHFhVFXCdW4OrqibxHe0vqFr0oT8akz3OcCT3RTa_GZn0NZdxfyd", "Idempotency-Key": "30tgu63s4ofhh54ks0000813", "Original-Request": "req_nMEF98Jn5B4gjw", "Request-Id": "req_nMEF98Jn5B4gjw", "Stripe-Should-Retry": "false", "Stripe-Version": "2022-11-15", "Vary": "Origin", "X-Stripe-Priority-Routing-Enabled": "true", "X-Stripe-Routing-Context-Priority-Tier": "api-testmode", "X-Wc": "ABGHI", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload"}, "status_code": 400}}]