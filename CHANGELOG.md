# Changelog

## Unreleased

- Upgrades Petal Pro to Tailwind 4!
- Updated: Adds `mix assets.build`
- Fixed: Seeder clears files
- Fixed: Combobox does not disappear when navigating to another page

## 2.2.0 - 2024-12-19

- Upgrades Petal Pro to LiveView 1.0.1!
- Content Editor component - based on Editor.js
- Blog/Content Management System - leveraging Content Editor
- Updated: address gettext deprecation issue (thanks @stollcri)
- Fixed: Sub-menu will display for Stacked Layout in mobile mode

## 2.1.0 - 2024-09-26

- Admin/User AI chat - use Lang<PERSON>hai<PERSON> to interrogate Petal Pro
- Updated: `petal.gen.live` uses Table instead of Data Table (and is based on streams). Use the `--data-table` option
  to generate templates using a Data Table
- Updated: Compatability increased for code generated by `phx.gen.live` and `phx.gen.html`
- Updated: Using latest Heroicons from Petal Components 2.0.6
- Fixed: User notifications marked as read based on `socket` url (previously only worked with `conn` request path)
- Fixed: Combobox has rounded corners after selection
- Fixed: sticky class for stacked layout

## 2.0.0 - 2024-08-15

- Petal Framework is merged into Petal Pro
- Npm packages recipe is merged in as default
- Persistent user notifications
- Graphs/stats dashboard in the Admin console
- See who's currently active (via Presence) in the Admin console
- If user belongs to only one org, current org is automatically set
- `User.is_admin` is replaced with `User.role`. User role is an enum
- Integrate `:typed_ecto_schema` with `Ecto.Schema` fallback for users who have not installed it.
- Orgs are added to log view
- Ecto insights tab is enabled in LiveDashboard
- Fixed: Edge case for Combo Box - Tom Select is re-generated when there is a change in option count
- Fixed: App does not crash when impersonating a user and the session has expired
- Fixed: Subscription logs weren't working!
- Fixed: Race condition with subscription create/update when responding to Stripe web hooks
- Fixed: In docker compose, postgres container will not auto-restart if stopped

### Upgrade Notes

Starting with this release, the Petal Framework is no longer referenced as an external package. If you
have generated a Dockerfile for deployment, then you can remove the following section:

```bash
# Remove the Petal repo:
RUN --mount=type=secret,id=PETAL_LICENSE_KEY \
  mix hex.repo add petal https://petal.build/repo \
    --fetch-public-key "SHA256:6Ff7LeQCh4464psGV3w4a8WxReEwRl+xWmgtuHdHsjs" \
    --auth-key $(cat /run/secrets/PETAL_LICENSE_KEY)
```

You may need to add `nodejs` and `npm`:

```bash
# install build dependencies
RUN apt-get update -y && apt-get install -y build-essential git nodejs npm \
    && apt-get clean && rm -f /var/lib/apt/lists/*_*
```

In the past `mix assets.setup` would install Tailwind CSS and esbuild. Tailwind is now installed
with `mix setup`, esbuild is installed via npm with `mix assets.deploy`. You'll need to remove:

```bash
# Remove:
RUN mix assets.setup
```

Finally, `lib/petal_pro_web/petal_framework_translations.ex` is no longer required. Components like the
DataTable can use Gettext directly.

## 1.8.0 - 2024-04-04

- JSON API: You can use the Petal Pro API for user management (register, sign in and update). It comes with OpenAPI support out of the box. Includes Swagger UI
- Update: Can sync all Stripe subscriptions including cancelled ones
- Added expanded user and org admin pages
- Fixed: PetalProWeb.Controllers.UserAuth.disconnect_user_liveviews doesn't disconnect the user's live views #216
- Fixed: Passwordless sign in issue from latest Phoenix Live View fixed #222
- Update: Sidebar layout now collapsible

### Upgrade Notes

Please refer to the [Petal Framework changelog notes for 0.8.0](https://github.com/petalframework/petal_framework/blob/main/CHANGELOG.md#080---2024-04-06) for details on breaking changes which must be addressed to adopt the collapsible sidebar layout.

## 1.7.0 - 2024-11-01

- Stripe integration - can buy subscriptions and manage them - support for both individuals and orgs
- Use `styler` for code formatting
- Updated landing page components to a more modern look

### Upgrade Notes

Starting in this release, we now use [Styler](https://github.com/adobe/elixir-styler) to format our codebase. This means there will be a large amount of changes. In order to minimise merge or rebase conflicts when upgrading, we recommend you swap out Recode with Styler first:

```elixir
# in mix.exs

# remove this:
{:recode, "~> 0.6", only: [:dev, :test]},

# and replace it with this:
{:styler, "~> 0.11", only: [:dev, :test], runtime: false},

# in .formatter.exs

# replace:
plugins: [Phoenix.LiveView.HTMLFormatter, Recode.FormatterPlugin]

# with:
plugins: [Phoenix.LiveView.HTMLFormatter, Styler]
```

Then, replace the content of `.credo.exs` with the one [in this repo](.credo.exs).

Finally, run the formatter for Styler to take care of things:

```elixir
mix deps.get && mix format
```

Another change we recommend you make before upgrading, especially if you've made changes to your landing page, is to rename `PetalProWeb.Components.LandingPage` to `PetalProWeb.LandingPageComponents`, make sure you also rename the file from `landing_page.ex` to `landing_page_components.ex`.

Given the amount of changes in this release, we recommend you backup your branch, then use `merge` instead of `rebase` to bring your branch up to date.

## 1.6.2 - 2023-11-03

- Fix bug where users registering via passwordless / 3rd parties would not get invites to orgs

## 1.6.1 - 2023-9-28

- Important passwordless security fix - anyone using passwordless should update
- Update deps

## 1.6.0 - 2023-7-05

### New

- <.image_upload> component for uploading single images like avatars. Used in user settings
- Local, Cloudinary and S3 support for <.image_upload>
- User impersonation - admins can login as any user without knowing their password - available via the Admin console (under the User menu).

### Updated

- All forms now use the new FormField structure
- <.public_layout> has been moved into public.html.heex (it's likely you'll want to modify this to your brand and hence a component doesn't make sense)

### Fixed

- Reset password page now accessible when logged in (previously would redirect)

### Removed

- Page builder (it was getting too complex to manage - let us know if you used this a lot and we'll consider bringing it back)

## 1.5.2 - 2023-4-20

### New

- Uses the `petal_framework` private package (deletes all the `_petal_framework` folders). This will allow for an easier upgrade process in the future, especially for components like the data table.
- Data Table supports `base_url_params`. Keep custom query params when changing sort order.
- Data Table is now self contained in the live view - code isn't scattered across the model and context.
- Added `mix recode` for code linting and refactoring
- Use `<.flash_group>` to show flash messages (Petal Framework 0.3.0)

### Changes

- Enabled Wallaby tests by default (`mix test`)
- Updated Tailwind to 3.3
- Updated Erlang to 25.3
- Added `rename_project` lib to replace custom mix task

### Breaking

- Data table API has changed slightly. See our docs https://petal.build/components/data-table on how to use

### Fixed

- Data table going blank when changing page size
- Navigation problems for the Data Table when setting the `default_limit` via a schema file
- Fix Finch issue with email sending

## 1.5.1 - 2023-3-10

### New

- Develop Petal Pro in GitHub CodeSpaces!
- Data Table column - can now filter by a select list
- Data Table column - can now filter by a float

### Changed

- topbar.js version updated to 2.0
- mix setup no longer fetches tailwind/esbuild if they already exist
- Upgrade Petal Components to 1.0
- Data Table is now a function component

### Fixed

- Github auth working again

## 1.5.0 - 2023-2-10

### Changed

- Upgraded Phoenix to 1.7
- Routes use the new verified routes
- Authentication pages converted to live views
- Upgraded fully from Hericons v1 to v2
- `petal.gen.live` now uses the Data Table component
- Tesla now uses the more secure Finch over Hackney (https://github.com/petalframework/petal_pro/issues/66)
- Confirmation page will redirect to org invitations if invitation exists (https://github.com/petalframework/petal_pro/issues/68)
- Removed Petal Enhance (it was more complex than we thought)

### Fixes

- Redirect to `/app/orgs` if an invalid org slug is used (https://github.com/petalframework/petal_pro/issues/70)
- When editing user via `/admin/users` - `patch_back_to_index` no longer crashes (https://github.com/petalframework/petal_pro/issues/61)
- Always show Data Table filters (https://github.com/petalframework/petal_pro/issues/60)

## 1.4.1 - 2022-11-15 16:30:00

### Added

- Add Petal Enhance (recipes)

### Fixes

- Fix admin users modal. Clicking off the modal now works
- Change avatar_url to text in DB for long URLs
- Whitelist Google profile picture images in CSP
- Fix Mailbluster unsubscribe route + documentation

## 1.4.0 - 2022-10-12 23:44:20

### Added

- Updated to LiveView 0.18 + Petal Components 0.18
- All components updated with declarative assigns (attr / slot)
- Data table component
- Local time component
- Sobelow for security checking
- Coveralls for code test coverage
- test.yml Github action for code quality
- Easily modifiable content security policy to help prevent XSS
- Added a docker-compose.yaml that adds Postgres so you don't need to install it

### Changed

- Router significantly more streamlined
- Some routes have been moved into their own files: AdminRoutes, AuthRoutes, DevRoutes, MailblusterRoutes
- Users are forced to be confirmed to access /app routes (easily configurable)
- Use Ecto enums for org roles

### Fixes

- Fix reset password failing when signed in
- Clean up dashboard_live.ex - some old code unused was left in there
- Improved SEO meta tags in \_head.html.eex
- Show a warning in the logs when no title/meta_desciprion is set on a public page
- Added `open-graph.png` (to be replaced by dev)
- Fix require_confirmed_user plug
- Fix landing page GSAP not working

## 1.3.0 - 2022-06-17 02:43:55

### Added

- Two-factor authentication using time-based one time passwords (paired with something like Google Authenticator)

### Changed

- Decoupled DashboardLive from Orgs so you can get started quicker if you don't want orgs
- Can pass a custom header class to the public layout
- Sidebar and stacked layouts now support grouped menu items (see dev_layout_component.ex for an example)
- Update Tailwind to 3.1
- Split CSS into different files thanks to Tailwind 3.1

### Fixed

- Onboarding now remembers user_return_to
- Fixed nav dropdown bug after modal toggle
- Fixed gettext in live views

## 1.2.0 - 2022-05-31 07:34:11

### Added

- Login with Google & Github - easy to add more auth providers
- Passwordless auth - register/sign in via a code sent to your email
- Orgs - create an org, invite & manage members, and more
- User lifecycle actions - run code after actions like register, sign_in, sign_out, password_reset, etc
- New generator: mix petal.gen.html (same args as phx.gen.html)
- New component: <.markdown content=""> & <.pretty_markdown content=""> (uses Tailwind Typography plugin)
- Added License and Privacy pages (with some content from a template to get you started)
- New layout: <.layout type="public">, for public marketing related pages like landing, about us, privacy, etc
- Hooks can now be run in dead views if compatible (see color-scheme-hook.js as an example)

### Changed

- Simpler config access (`PetalPro.config(:app_name)` instead of `Application.get_env(:petal_pro, :app_name)`)
- Refactor <.layout> to take less props
- Refactor dark/light mode system. Much simpler now and no longer needs cookies
- Put Petal Pro Components in their own folder for easier future upgrades (can duplicate if you want to modify them)
- Sidebar and stacked layout have a new slot for the top right corner (if you want to add something like a notifications bell)

### Fixed

- Log metadata wasn't being cast
- More user actions are logged
- Fixed petal.live generator tests
- Added tests for user settings live views

## 1.1.1 - 2022-03-12 20:45:36

- Bump Oban version to stop random error showing
- Bump Petal Components
- Use new <.table> component in petal.gen.live generator & logs
- Dark mode persists on all pages with cookies
- Fix logo showing twice in emails
- Improved the Fly.io deploy flow
- Fix admin user search
- Remove guide (this is now online)

## 1.1.0 - 2022-02-28 00:08:52

- Added generator `mix petal.gen.live`
- Add gettext throughout public facing templates
- Improved dev_guide
- Add Oban
- Easy Fly.io deployment
