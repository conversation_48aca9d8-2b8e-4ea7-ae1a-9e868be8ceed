# This file represents 1 workflow. A workflow has multiple jobs. A job has multiple tasks.
name: Elixir Testing CI

# This workflow runs every push
on: push

# If you'd like it only to run on certain branches:
# on:
#   push:
#     branches: [main, dev]
#   pull_request:
#     branches: [staging, prod]

# Jobs are run in parallel
jobs:
  # This is our one and only job
  test:
    runs-on: ubuntu-latest
    services:
      # We need postgres for our tests
      postgres:
        image: postgres:15.7

        # Match these to what's defined in dev.exs
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres

        # Maps tcp port 5432 on service container to the host
        ports: ["5432:5432"]

        # Set health checks to wait until postgres has started
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5

    env:
      MIX_ENV: test
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

    # A job has any number of steps: we will define them here
    # Each dash (-) represents a step. Sometimes we give them a custom name, sometimes we leave it if it's obvious.
    steps:
      # Our OS filesystem has nothing on it - let's checkout our codebase using a pre-made Github step
      - uses: actions/checkout@v4

      # Our OS won't have Elixir installed - let's install it with another pre-made step
      # Docs: https://github.com/erlef/setup-beam
      - name: Setup Elixir
        uses: erlef/setup-beam@v1
        with:
          version-type: strict
          version-file: .tool-versions

      # We might as well re-use our deps instead of downloading them over and over. So let's use a cache.
      # Docs: https://github.com/actions/cache
      - name: Cache Mix
        id: mix-cache
        uses: actions/cache@v4
        with:
          path: |
            deps
            _build
          key: ${{ runner.os }}-mix-${{ hashFiles('**/mix.lock') }}
          restore-keys: |
            ${{ runner.os }}-mix-

      - name: Cache JS deps
        id: node-cache
        uses: actions/cache@v4
        with:
          path: |
            assets/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/assets/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install deps (if not cached)
        if: steps.mix-cache.outputs.cache-hit != 'true'
        run: |
          mix local.rebar --force
          mix local.hex --force
          mix deps.get

      - name: Install JS deps (if not cached)
        if: steps.node-cache.outputs.cache-hit != 'true'
        run: npm i --prefix assets

      # `mix format` is now mature enough that we can enforce it
      - name: Check that `mix format` has been run
        run: mix format --check-formatted

      - name: Check warnings
        run: mix compile --warnings-as-errors

      - name: Create the test database
        run: mix ecto.create ; mix ecto.migrate

      - name: Run tests
        run: |
          mix assets.deploy
          mix test

      - name: Check Credo Warnings
        run: mix credo --strict --only warning

      - name: Look for security issues
        run: mix sobelow --config

      - name: Archive test failure screenshots if any
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: test-failure-screenshots
          path: screenshots/
