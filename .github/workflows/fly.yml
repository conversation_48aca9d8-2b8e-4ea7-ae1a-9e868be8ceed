name: Deploy to fly
on:
  push:
    branches:
      - fly
jobs:
  deploy:
    name: Deploy proxy
    runs-on: ubuntu-22.04
    steps:
      # This step checks out a copy of your repository.
      - uses: actions/checkout@v4
      # This step runs `flyctl deploy`.
      - uses: superfly/flyctl-actions@master
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
        with:
          args: "deploy --remote-only"
